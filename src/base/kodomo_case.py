import datetime
import glob
import json
import logging
import shutil
import os
import time
import unittest
from urllib.parse import urljoin

from selenium.webdriver import Keys

from base.driver import WebRingsSiteTestDriver
from base.snap_store import WebRingsSiteTestSnapShotInfo
from util.helper import Web<PERSON><PERSON><PERSON><PERSON>leHelper
from util.config import CommonSettings
from util.dbutil import SQL<PERSON>andler
from selenium.common.exceptions import NoSuchElementException
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains


from util.config import CommonSettings
__settings = CommonSettings.get_settings()
__logger = logging.getLogger("kodomo.test.log")
__logger.setLevel(logging.DEBUG)
__handler = logging.FileHandler(filename=os.path.join(__settings.log_dir, "kodomo-test.log"), mode="w")
__formatter = logging.Formatter('%(asctime)s - %(levelname)s:%(name)s - %(message)s')
__handler.setFormatter(__formatter)
__logger.addHandler(__handler)


class KodomoSiteTestCaseBase(unittest.TestCase):

    env_name = "kodomo_std"
    settings = None
    settings_dict = {}
    test_data_root_path = ""
    test_sql_root_path = ""
    test_data = {}
    common_test_data = {}
    test_sql = ""
    jichitai_code = ""
    expandedElements = []

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 最初にドライバーの初期化入るとおかしくなるので無し setup内でやる
        # self.driver = WebRingsSiteTestDriver.get_driver()
        self.driver = None
        self.settings = CommonSettings.get_settings()
        self.settings_dict = self.settings.settings_json.get(self.env_name, {})
        self.logger = logging.getLogger("kodomo.test.log")
    
    @classmethod
    def setUpClass(cls):
        cls.settings = CommonSettings.get_settings()
        cls.settings_dict = cls.settings.settings_json.get(cls.env_name, {})
        cls.jichitai_code = cls.settings_dict.get("jichitai_code","")
        cls.test_data_root_path = os.path.join(cls.__get_test_root_path(),"testdata")
        cls.test_sql_root_path = os.path.join(cls.__get_test_root_path(),"testsql")

        # テストクラスに対応するデータ
        data_path = cls.__get_test_data_path()
        if(os.path.exists(data_path)):
            with open(data_path, mode="r",encoding="utf-8") as f:
                cls.test_data = json.load(f)
        # testdataフォルダに存在する共通テストデータ
        data_path = cls.__get_common_test_data_path()
        if(os.path.exists(data_path)):
            with open(data_path, mode="r",encoding="utf-8") as f:
                cls.common_test_data = json.load(f)
        
    @classmethod
    def tearDownClass(cls):
        pass
        
    def exec_sqlfile(self, file_name, params=None):
        test_sql_path = os.path.join(self.get_current_sql_dir(), file_name)
        test_sql_text = ""
        if(os.path.exists(test_sql_path)):
            print("SQL実行", test_sql_path)
            _db = self.get_db()
            _db.exec_sqlfile(test_sql_path, params=params)
        else:
            print(f"指定されたSQLファイルが存在しません。: {test_sql_path}")

    def setUp(self):
        # Driverの初期化処理
        self.driver = WebRingsSiteTestDriver.get_driver()
        self.actionChains = ActionChains(self.driver)
        self.accept_next_alert = True
        test_sql_path = self.get_current_setup_sql_path()
        test_sql_text = ""
        if(os.path.exists(test_sql_path)):
            print("SQL実行", test_sql_path)
            _db = self.get_db()
            _db.exec_sqlfile(test_sql_path)

    def tearDown(self, removeTab=True):
        if removeTab:
            try:
                els = self.find_elements_by_css_selector("a.tab_title")
                for el in els:
                    try:
                        el.click()
                        self.find_element_by_css_selector("span.removeTab").click()
                    except:
                        continue
            except:
                pass

        test_sql_path = self.get_current_teardown_sql_path()
        if(os.path.exists(test_sql_path)):
            print("SQL実行", test_sql_path)
            _db = self.get_db()
            _db.exec_sqlfile(test_sql_path)
    
    def get_db(self, env_name=""):
        db_param = self.get_dbparams()
        sql_handler = SQLHandler(
            p_jichitai_code=self.jichitai_code,
            p_server=db_param.get("host",""),
            p_db="master",
            p_user=db_param.get("user_id",""),
            p_pass=db_param.get("password",""),
            p_catalogs=db_param.get("catalogs",""),
        )
        return sql_handler

    def get_dbparams(self, env_name=""):
        if env_name == "":
            return self.settings_dict.get("db",{})
        else:
            settings = self.settings.settings_json.get(self.env_name, {})
            return settings.get("db",{})
        
    @classmethod
    def __get_test_root_path(cls):
        settings = CommonSettings.get_settings()
        module_paths = cls.get_current_module_name().split(".")
        return os.path.join(settings.src_dir,module_paths[0],module_paths[1])

    @classmethod
    def __get_test_data_path(cls):
        settings = CommonSettings.get_settings()
        module_paths = cls.get_current_module_name().split(".")
        return os.path.join(cls.test_data_root_path, module_paths[-1]+".json")

    @classmethod
    def __get_common_test_data_path(cls):
        settings = CommonSettings.get_settings()
        module_paths = cls.get_current_module_name().split(".")
        return os.path.join(cls.test_data_root_path, "common_test_data.json")

    @classmethod
    def get_current_module_name(cls):
        return cls.__module__

    @classmethod
    def get_current_class_name(cls):
        return cls.__name__
    
    @classmethod
    def get_current_class_full_name(cls):
        return ".".join([cls.__module__, cls.__name__])
    
    def get_current_method_name(self):
        return self.id().split(".")[-1]

    def get_current_sql_dir(self):
        self.test_sql_root_path
        module_paths = self.get_current_module_name().split(".")
        return os.path.join(self.test_sql_root_path)

    def get_current_setup_sql_path(self):
        self.test_sql_root_path
        module_paths = self.get_current_module_name().split(".")
        return os.path.join(self.test_sql_root_path, module_paths[-1], self.get_current_method_name()+"-setup.sql")

    def get_current_teardown_sql_path(self):
        self.test_sql_root_path
        module_paths = self.get_current_module_name().split(".")
        return os.path.join(self.test_sql_root_path, module_paths[-1], self.get_current_method_name()+"-teardown.sql")

    def do_login(self, user_id=None, password=None):
        """ログイン→メインメニューの遷移"""
        if user_id is None or password is None:
            user_id = self.settings_dict.get("user_id","")
            password = self.settings_dict.get("password","")
        
        login_url = urljoin(self.settings_dict.get("base_url",""), self.settings_dict.get("login_url",""))
        self.get(login_url)
        self.wait_page_loaded()
        self.send_keys_by_id(self.settings_dict.get("user_id_element",""), user_id)
        self.send_keys_by_id(self.settings_dict.get("password_element",""), password)
        self.click_by_id(self.settings_dict.get("login_element",""))
        # self.click_by_id(self.settings_dict.get("fukushi_btn_element",""))

    def do_login_new_tab(self, user_id=None, password=None):
        """ログイン→メインメニューの遷移"""
        if user_id is None or password is None:
            user_id = self.settings_dict.get("user_id", "")
            password = self.settings_dict.get("password", "")

        login_url = urljoin(self.settings_dict.get("base_url", ""), self.settings_dict.get("login_url", ""))
        self.get(login_url)
        self.wait_page_loaded()
        self.send_keys_by_id(self.settings_dict.get("user_id_element", ""), user_id)
        self.send_keys_by_id(self.settings_dict.get("password_element", ""), password)
        self.click_by_id(self.settings_dict.get("login_element", ""))
        old_window_list = self.driver.window_handles[:]
        elm = self.click_by_id(self.settings_dict.get("kodomo_btn_element", ""))
        # 現在表示したタブのハンドルに切り替え
        wait = WebDriverWait(self.driver, 10)
        wait.until(EC.number_of_windows_to_be(len(old_window_list) + 1))
        for window_handle in reversed(self.driver.window_handles):
            if window_handle not in old_window_list:
                self.driver.close()
                self.driver.switch_to.window(window_handle)
                break

    def goto_batch_execute(self, user_id=None, password=None, screen_shot_name="", login_flg=True):
        """バッチ処理：ログイン→メインメニュー→バッチ管理→即時実行→スケジュール個別追加へ遷移"""
        if login_flg:
            self.do_login(user_id=user_id, password=password)
        if screen_shot_name != "":
            self.screen_shot(screen_shot_name)
        self._goto_batch_kanri()
        self._goto_immediate_execute()
        self._goto_schedule_add()

    def _goto_batch_kanri(self):
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/span").click()
        time.sleep(1)

    def _goto_immediate_execute(self):
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/ul/li[3]/span").click()
        time.sleep(1)

    def _goto_schedule_add(self):
        scheduleAddButton = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/ul/li[3]/ul/li[1]/span/a")
        self.actionChains.double_click(scheduleAddButton).perform()
        time.sleep(1)

    def kennsakujyoukenn_nyuuryoku(self, gyomuNM, subSystemNM, shoriNM, tab_index=1):
        """バッチ処理：スケジュール個別追加画面: 業務名=子ども子育て支援、サブシステム名=入所、 処理名=入退所異動処理"""
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZEAF000400_SelKensakuGyomuNM_select", text=gyomuNM)
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZEAF000400_SelKensakuSubSystemNM_select", text=subSystemNM)
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZEAF000400_SelKensakuShoriNM_select", text=shoriNM)
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF000400_BtnKensaku_button")

    def goto_kodomo_setaijyouhou_search(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→世帯情報検索へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_setaijyouhou()
        self._goto_kennsaku()        

    def _goto_setaijyouhou(self):
        '''世帯情報'''
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[1]/span").click()
        time.sleep(1)

    def _goto_kennsaku(self):
        '''検索'''
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li/ul/li/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

    def goto_kodomo_riyoutyousei_jidousennkoujidoukennsaku(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→入所自動選考結果（児童検索）へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_riyoutyousei()
        self._goto_jidousennkoukekka_jidoukennsaku()

    def goto_kodomo_nyuusyokannri_gennkyoukesikomi(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→現況消込へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_nyuusyokannri()
        self._goto_gennkyoukesikomi()

    def _goto_gennkyoukesikomi(self):
        '''現況消込'''
        # menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[10]/ul/li[3]/span")
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[8]/ul/li[3]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def goto_kodomo_shisetsujigyosya_add(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→施設事業者へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_shisetsujigyosyaninnkajigyousya_1()
        self._goto_shisetsujigyosya_add()
    
    def _goto_shisetsujigyosyaninnkajigyousya_1(self):
        '''施設事業者（認可事業者）'''
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[5]/span").click()
        time.sleep(1)

    def _goto_shisetsujigyosya_add(self):
        '''追加'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[5]/ul/li[2]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def goto_kodomo_shisetsujigyosho_add(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→施設事業所へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[6]/span").click()
        time.sleep(1)
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[6]/ul/li[2]/span")
        self.actionChains.double_click(menu).perform()

    def goto_kodomo_nyuusyokannri_nyuusyomousikomikennsaku(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→入所・申込検索へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_nyuusyokannri()
        self._goto_nyuusyomousikomikennsaku()
        
    def goto_kodomo_shisetsukanri_search(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→施設検索へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_shisetuskanri()
        self._goto_shisetsukensaku()

    def _goto_shisetuskanri(self):
        '''施設管理'''
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[7]/span").click()
        time.sleep(1)

    def _goto_shisetsukensaku(self):
        '''施設検索'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[7]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def goto_kodomo_ninkagaishinseikanri_search(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→認可外申請管理へ遷移"""
        self.do_login(user_id=user_id, password=password)
        # self._goto_kodomo()
        self._goto_ninnkagaisinnseikannri()
        self._goto_ninnkagaisinnseikensaku()        
        
    def goto_kodomo_matsuta_shisetuhoikuryou(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→施設保育料等へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_masutakannri()
        self._goto_shisetuhoikuryou()
        
    def goto_kodomo_matsuta_kunikaisou(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→国階層へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_masutakannri()
        self._goto_kunikaisou()

    def goto_kodomo_nyuusyokannri_jidoukennsaku(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→児童検索へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_nyuusyokannri()
        self._goto_jidoukennsaku()

    def goto_menu(self,menu_path,menu_number='0'):
        root:WebElement = self.find_element_by_xpath("//ul[@id='rootmenu_"+ menu_number +"']")
        global menuList
        menuList = root.find_elements(By.XPATH,("./*"))
        def _goto_menu(target):
            global menuList
            for el in menuList:
                names = el.text.split("\n")
                if names[0] == target:
                    menuRoot:list[WebElement] = el.find_elements(By.XPATH,("./ul"))
                    if len(menuRoot) >0 :
                        if menuRoot[0].is_displayed() == False:
                            if target != menu_path[-1]:
                                el.find_element(By.TAG_NAME,"span").click()
                                time.sleep(2)
                        menuList = el.find_elements(By.XPATH,("./ul/*"))
                    else:
                        if target == menu_path[-1]:
                            lastMenu = el.find_element(By.TAG_NAME,"span")
                            self.actionChains.double_click(lastMenu).perform()
                            time.sleep(2)
                        else:
                            return False
                    return True
                else:
                    continue
            return False
        
        for menu in menu_path:
            if _goto_menu(menu):
                continue
            else:
                raise Exception("メニュー名が見つかりません。")
       
    def goto_kodomo_nyuusyokannri_jidoukennsaku(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→児童検索へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_nyuusyokannri()
        self._goto_jidoukennsaku()

    def goto_kodomo_nyuusyokannri_keiyakujyouhouikkatutouroku(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→契約情報一括登録へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_nyuusyokannri()
        self._goto_keiyakujyouhouikkatutouroku()

    def goto_kodomo_kyuufuhikannri_seikyuusiharaikennsaku(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→請求支払検索へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_kyuufuhikannri()
        self._goto_seikyuusiharaikennsaku()

    def goto_kodomo_kyuufuhikannri_kuniriyouhisu(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→支弁台帳用国利用日数へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_kyuufuhikannri()
        self._goto_kuniriyouhisu()

    def goto_kodomo_ninnkagaisinnseikannri_ninnkagaisinnseikensaku(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→認可外申請検索へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_ninnkagaisinnseikannri()
        self._goto_ninnkagaisinnseikensaku()        

    def goto_kodomo_matsuta_nyuusyosennkousisu(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→入所選考指数へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_masutakannri()
        self._goto_nyuusyosennkousisu() 

    def goto_kodomo_riyoutyusei_riyoujyoukyou_search(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→施設利用状況検索へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_riyoutyousei()
        self._goto_riyoujyoukyou_search() 
    def goto_kodomo_kyouseisyuusei_jyuuminbangouhenkou(self, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→強制修正→住民番号変更へ遷移"""
        self.do_login(user_id=user_id, password=password)
        self._goto_kodomo()
        self._goto_kyouseisyuusei()
        self._goto_jyuuminbangouhenkou() 

    def _goto_kodomo(self):
        '''子ども子育て支援'''
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/span").click()
        time.sleep(1)

    def _goto_ninnkagaisinnseikannri(self):
        '''認可外申請管理'''
        # self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[14]/span").click()
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[12]/span").click()
        time.sleep(1)

    def _goto_ninnkagaisinnseikensaku(self):
        '''認可外申請検索'''
        # menu =self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[14]/ul/li[1]/span")
        menu =self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[12]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_riyoutyousei(self):
        '''利用調整'''
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[2]/span").click()
        time.sleep(1)
    def _goto_kyouseisyuusei(self):
        '''強制修正'''
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[3]/span").click()
        time.sleep(1)

    def _goto_masutakannri(self):
        '''マスタ管理'''
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/span").click()
        time.sleep(1)

    def _goto_kaikyokubi(self):
        '''開局日'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[6]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_hitorioyasetai(self):
        '''ひとり親世帯等減免'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[7]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_syokuzairyouhi(self):
        '''食材料費単価'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[8]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
        
    def _goto_shisetuhoikuryou(self):
        '''施設保育料等'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[9]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_kunikaisou(self):
        '''国階層'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_kunicyousyuukin(self):
        '''国徴収金'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[2]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_shicyousyuukin(self):
        '''市徴収金'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[3]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_tsuuchibunsyo(self):
        '''通知文書'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[4]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_nyuusyosennkousisu(self):
        '''入所選考指数'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[4]/ul/li[5]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_nyuusyokannri(self):
        '''入所管理'''
        # self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[10]/span").click()
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[8]/span").click()
        time.sleep(1)

    def _goto_kyuufuhikannri(self):
        '''給付費管理'''
        # self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[11]/span").click()
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[9]/span").click()
        time.sleep(1)

    def _goto_seikyuusiharaikennsaku(self):
        '''請求支払検索'''
        # menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[11]/ul/li[1]/span")
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[9]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
    
    def _goto_kuniriyouhisu(self):
        '''支弁台帳用国利用日数'''
        # menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[11]/ul/li[2]/span")
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[9]/ul/li[2]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_keiyakujyouhouikkatutouroku(self):
        '''契約情報一括登録'''
        # menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[10]/ul/li[2]/span")
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[8]/ul/li[2]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_jidoukennsaku(self):
        '''児童検索'''
        # menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[10]/ul/li[1]/span")
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[8]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_nyuusyomousikomikennsaku(self):
        '''入所・申込検索'''
        # menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[10]/ul/li[4]/span")
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[8]/ul/li[4]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_jidousennkoukekka_sisetuninnzuukakuninnhennkou(self):
        '''入所自動選考結果（施設人数確認変更）'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[2]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_jidousennkoukekka_jidoukennsaku(self):
        '''入所自動選考結果（児童検索）'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[2]/ul/li[2]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_jidousennkoukekka_doutennsisetukakuninn(self):
        '''入所自動選考結果（同点施設確認）'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[2]/ul/li[3]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
        
    def _goto_riyoujyoukyou_search(self):
        '''施設利用状況検索）'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[2]/ul/li[5]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def _goto_taikisya_search(self):
        '''待機者管理'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[2]/ul/li[6]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
    def _goto_jyuuminbangouhenkou(self):
        '''住民番号変更'''
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[10]/ul/li[3]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)

    def wait_page_loaded(self, wait_timeout=10):
        """ページが完全に読み込まれるまで待機する"""
        WebDriverWait(self.driver, wait_timeout).until(
            lambda d: d.execute_script(
                "return document.readyState") == "complete"
        )
        try:
            WebDriverWait(self.driver, wait_timeout).until(
                EC.invisibility_of_element_located((By.ID, "progressArea"))
            )
        except:
            pass

    def get(self, url):
        return WebDriverHandleHelper.get(self.driver, url)

    def find_element(self, method_type, value):
        """既存移行用"""
        if(method_type == By.ID):
            return self.find_element_by_id(value)
        elif(method_type ==  By.NAME):
            return self.find_element_by_name(value)
        elif(method_type ==  By.XPATH):
            return self.find_element_by_xpath(value)
        else:
            return self.find_element_by_css_selector(value)

    def select_Option(self, dummy, elem, label):
        WebDriverHandleHelper.select_option(elem, text=label)

    def find_element_by_name(self, name):
        """指定されたNAME属性を持つ要素を取得"""
        return WebDriverHandleHelper.find_element_by_name(self.driver, name)

    def find_element_by_id(self, idstr):
        """指定されたID属性を持つ要素を取得"""
        return WebDriverHandleHelper.find_element_by_id(self.driver, idstr)
        
    def find_element_by_xpath(self, xpath):
        """指定されたXPATHの要素を取得"""
        return WebDriverHandleHelper.find_element_by_xpath(self.driver, xpath)
        
    def find_element_by_css_selector(self, selector):
        """指定されたCSSセレクタに該当する最初の要素を取得"""
        return WebDriverHandleHelper.find_element_by_css_selector(self.driver, selector)

    def find_elements_by_css_selector(self, selector):
        """指定されたCSSセレクタに該当する全要素を取得(要素のListを返却)"""
        return WebDriverHandleHelper.find_elements_by_css_selector(self.driver, selector)

    def find_element_by_tag_name(self, name):
        """指定されたTAG名の要素の内最初に取得出来たものを返却"""
        return WebDriverHandleHelper.find_element_by_tag_name(self.driver, name)

    def send_keys_by_name(self, name, value):
        """指定されたNAME属性を持つ要素にvalueのキーボード入力を行う"""
        WebDriverHandleHelper.send_keys_by_name(self.driver, name, value)

    def send_keys_by_id(self, idstr, value):
        """指定されたID属性を持つ要素にvalueのキーボード入力を行う"""
        WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value)

    def click_by_name(self, name):
        """指定されたNAME属性を持つ要素でClickを行う"""
        WebDriverHandleHelper.click_by_name(self.driver, name)
        
    def click_by_id(self, idstr):
        """指定されたID属性を持つ要素でClickを行う"""
        WebDriverHandleHelper.click_by_id(self.driver, idstr)
        self.wait_page_loaded(wait_timeout=5)


    def click_button_by_label(self, label):
        """指定されたラベルのボタンをクリックする"""
        button_elem = None
        for v in self.find_elements_by_css_selector("button"):
            if label == v.text.strip():
                button_elem = v
                break
        if button_elem is None:
            raise NoSuchElementException(msg=f"{label} のボタンが見つかりませんでした。")
        button_elem.click()

    def click_batch_job_button_by_label(self, job_label, tab_index=1):
        pages = self.find_element_by_xpath(
            '//*[@id="tab0' + str(tab_index) + '_ZZZ000000_TblSchedule_spanTotalPage"]').text
        for page in range(int(pages)):
            tr_elem = self.find_elements_by_css_selector(
                "#tab0" + str(tab_index) + "_ZZZ000000_TblSchedule > tbody > tr")
            table_idx = 0
            td_elem_no = None
            td_elem_jobunetto_mei = None
            text_no = ""
            text_jobunetto_mei = 0
            for elem in tr_elem:
                table_idx += 1
                if table_idx == 1:
                    td_elem_no = elem.find_element(By.CSS_SELECTOR, "td.ui-valign-middle.ui_wrtable_border_left")
                if table_idx == 2:
                    td_elem_jobunetto_mei = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)")
                    table_idx = 0
                if td_elem_no is not None:
                    text_no = td_elem_no.text
                if td_elem_jobunetto_mei is not None:
                    if td_elem_jobunetto_mei.text:
                        text_jobunetto_mei = td_elem_jobunetto_mei.text.split(") ", 1)[1]
                if job_label == text_jobunetto_mei:
                    self.click_by_id("tab0" + str(tab_index) + "_ZZZ000000_BtnMode32_" + str(text_no) + "_4_button")
                    return

            # Click next page
            nextPageBtn = self.find_element_by_xpath(
                '//*[@id="tab0' + str(tab_index) + '_ZZZ000000_TblSchedule_aftBtn_button"]')
            if nextPageBtn.is_displayed():
                nextPageBtn.click()
                self.wait_page_loaded(wait_timeout=5)

    def execute_script(self, script_text):
        """指定されたScriptの実行"""
        WebDriverHandleHelper.execute_script(self.driver, script_text)

    def set_job_params(self, job_params):
        for v in job_params:
            self.set_job_param(v["title"], v["type"], v["value"])

    def set_job_param(self, title, param_type, value):
        for v in self.find_elements_by_css_selector("td.wr_Css_table_LB"):
            if title == v.text.strip():
                if param_type == "text":
                    param_input = v.find_element(By.XPATH, '../td/input')
                    param_input.click()
                    param_input.send_keys("")
                    param_input.send_keys(value)
                if param_type == "select":
                    param_input = v.find_element(By.XPATH, '../td/select')
                    WebDriverHandleHelper.select_option(param_input, text=value)
                if param_type == "check":
                    param_input = v.find_element(By.XPATH, './input')
                    id_value = param_input.get_attribute("id")
                    id_value = id_value.replace("check","item")
                    input_elem = self.find_element_by_id(id_value)
                    if param_input.is_selected():
                        if value == "0":
                            param_input.click()
                            # self.driver.execute_script("arguments[0].setAttribute('value',arguments[1])",input_elem, value)
                    else:
                        if value == "1":
                            param_input.click()
                            # self.driver.execute_script("arguments[0].setAttribute('value',arguments[1])",input_elem, value)
                break

    def exec_batch_job(self):
        """ジョブの実行"""
        exec_button = self.find_element_by_id("ExecuteButton")
        exec_button.click()
        self.alert_ok()
        return datetime.datetime.now()

    def exec_batch_job_kodomo(self, tab_index=1):
        """ジョブの実行"""
        exec_button = self.find_element_by_id("tab0" + str(tab_index) + "_ZEAF002200_executebtn_button")
        exec_button.click()
        time.sleep(2)
        start_time = self.find_elements_by_css_selector(
                        "#tab0" + str(tab_index) + "_ZZZ000000_LblStartTime_1_7")[0].text.strip()
        start_datetime = datetime.datetime.strptime(start_time, "%Y/%m/%d %H:%M:%S")
        return start_datetime

    def click_job_exec_log(self):
        """ジョブの実行履歴ボタンクリック"""
        exec_button = self.find_element_by_id("JobListButton")
        exec_button.click()

    def click_job_exec_log_search(self):
        """ジョブの実行履歴の検索ボタンクリック"""
        exec_button = self.find_element_by_id("SearchButton")
        exec_button.click()

    def click_job_exec_log_search_kodomo(self):
        """ジョブの実行履歴の検索ボタンクリック"""
        click_times = 3
        while click_times >0 :
            try:
                self.click_button_by_label("検索")
                break
            except:
                if click_times > 0:
                    time.sleep(5)
                    click_times = click_times - 1
                else:
                    break

    def click_report_log(self):
        """ジョブの帳票履歴ボタンクリック"""
        exec_button = self.find_element_by_id("ReportListButton")
        exec_button.click()

    def click_job_list(self):
        """ジョブの処理一覧ボタンクリック"""
        exec_button = self.find_element_by_id("ExecListButton")
        exec_button.click()

    def wait_job_finished(self, limit_wait_count=10, time_span_sec=3):
        """ジョブ実行履歴の一覧に処理中が無くなるまで待機"""
        self.click_job_exec_log_search()
        list_tbody_elem = None
        for count in range(limit_wait_count):
            all_done = True
            for v in self.find_elements_by_css_selector("th.wr_table_title"):
                if "状態" == v.text.strip():
                    list_tbody_elem = v.find_element(By.XPATH, "../..")
                    break
            for v in list_tbody_elem.find_elements(By.XPATH, "./tr"):
                for elem in v.find_elements(By.XPATH, "./td[2]"):
                    if elem.text.strip() == "処理中":
                        all_done = False
                        break
            if(all_done):
                break
            self.wait_page_loaded(wait_timeout=time_span_sec)
            self.click_job_exec_log_search()

    def wait_job_finished_kodomo(self, limit_wait_count=10, time_span_sec=3):
        """ジョブ実行履歴の一覧に"実行待ち"と"実行中"が無くなるまで待機"""
        msg_job = ""
        startTime = datetime.datetime.now()
        time.sleep(5)        
        self.click_job_exec_log_search_kodomo()
        list_tbody_elem = None
        result = False
        waitTime = datetime.datetime.now() - startTime
        while waitTime.seconds < limit_wait_count:
            for v in self.find_elements_by_css_selector("span.ui_wrlabel_text"):
                if "状態" == v.text.strip():
                    list_tbody_elem = v.find_element(By.XPATH, "../../../../../tbody")
                    break
            elem = list_tbody_elem.find_element(By.XPATH, "./tr[3]/td[6]/div/span")
            msg_job = elem.text.strip()
            if elem.text.strip() == "正常終了":
                result = True
                break
            if elem.text.strip() == "異常終了":
                break
            self.click_job_exec_log_search_kodomo()
            self.wait_page_loaded(wait_timeout=time_span_sec)
            waitTime = datetime.datetime.now() - startTime
        
        self.assertTrue(expr=result, msg=msg_job)


    def get_job_report_pdf(self, exec_datetime, case_name=""):
        """実行時間以降に作成されたPDFをDLする"""
        base_datetime = exec_datetime - datetime.timedelta(seconds=3)  # 3秒前
        self.click_job_exec_log_search()
        list_tbody_elem = None
        download_count = 0
        downloaded_list = []
        is_continue = True
        while(is_continue):
            for v in self.find_elements_by_css_selector("th.wr_table_title"):
                if "開始頁" == v.text.strip():
                    list_tbody_elem = v.find_element(By.XPATH, "../..")
                    break
            for v in list_tbody_elem.find_elements(By.XPATH, "./tr"):
                is_target_row = False
                wk_datetime_text = ""
                for elem in v.find_elements(By.XPATH, "./td[5]"):
                    wk_datetime_text = elem.text.strip()
                    start_datetime = datetime.datetime.strptime(wk_datetime_text, "%Y/%m/%d %H:%M:%S")
                    if(start_datetime >= base_datetime):
                        if(elem.text.strip() not in downloaded_list):
                            is_target_row = True
                if(is_target_row):
                    for x in v.find_elements(By.XPATH, "./td/button"):
                        x.click()
                        download_done = True
                        download_count += 1
                        downloaded_list.append(wk_datetime_text)
                        self.wait_page_loaded(wait_timeout=10)
                    break
                else:
                    is_continue = False
            
        if(download_count > 0):
            # 少し待たないと一時ファイルのままになっている。
            self.wait_page_loaded(wait_timeout=10)

            store = WebRingsSiteTestSnapShotInfo.get_store()
            
            # 一時フォルダから名前を変更して移動
            for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
                file_base_name = os.path.basename(file_path)
                # print(file_base_name)
                save_file_path = self.__get_download_file_path(file_base_name, case_name)
                shutil.move(file_path, save_file_path)
                store.add_download_file(save_file_path, self)
        return download_count

    def goto_output_files_dl_page(self, exec_datetime):
        """ジョブの実行履歴から指定された実行日時付近の実行結果の作成データのページへ遷移"""
        base_datetime = exec_datetime - datetime.timedelta(seconds=3)  # 3秒前
        self.click_job_exec_log_search()
        is_dl_page = False
        list_tbody_elem = None
        target_dl_page_button = None
        for v in self.find_elements_by_css_selector("th.wr_table_title"):
            if "状態" == v.text.strip():
                list_tbody_elem = v.find_element(By.XPATH, "../..")
                break
        for v in list_tbody_elem.find_elements(By.XPATH, "./tr"):
            is_target_row = False
            for elem in v.find_elements(By.XPATH, "./td[5]"):
                start_datetime = datetime.datetime.strptime(elem.text.strip(), "%Y/%m/%d %H:%M:%S")
                if(start_datetime >= base_datetime):
                    is_target_row = True
            if(is_target_row):
                for x in v.find_elements(By.XPATH, "./td[7]/button"):
                    is_dl_page = True
                    target_dl_page_button = x
                    break
        if(is_dl_page and target_dl_page_button is not None):
            target_dl_page_button.click()
        else:
            is_dl_page = False
        return is_dl_page

    def get_job_output_files(self, case_name=""):
        """ダウンロードページにいる前提で全てのファイルをDL"""
        download_count = 0
        for v in self.find_elements_by_css_selector("button.NOLOCK"):
            if(v.get_attribute("id").strip().startswith("No")):
                download_count += 1
                v.click()
        if(download_count > 0):
            # 少し待たないと一時ファイルのままになっている。
            self.wait_page_loaded(wait_timeout=10)

            store = WebRingsSiteTestSnapShotInfo.get_store()
            # 一時フォルダから名前を変更して移動
            for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
                file_base_name = os.path.basename(file_path)
                save_file_path = self.__get_download_file_path(file_base_name, case_name)
                shutil.move(file_path, save_file_path)
                store.add_download_file(save_file_path, self)
        return download_count

    def get_job_report_kodomo(self, exec_datetime, case_name="", tab_index=1, report_name=None):
        download_count = 0
        index_download = 0

        pages = self.find_element_by_xpath(
            '//*[@id="tab0' + str(tab_index) + '_ZZZ000000_TblNohimbutsu_spanTotalPage"]').text
        for page in range(int(pages)):
            is_not_execute_time = False
            is_found_report_name = False
            index_td = 1
            while True:
                tr_elem = self.find_elements_by_css_selector(
                    "#tab0" + str(tab_index) + "_ZZZ000000_TblNohimbutsu > tbody > tr")
                if index_td >= len(tr_elem):
                    break

                td = tr_elem[index_td]
                try:
                    td_wk_datetime_text = td.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text.strip()
                    start_datetime = datetime.datetime.strptime(td_wk_datetime_text, "%Y/%m/%d %H:%M:%S")
                except Exception:
                    report_name_elm = td.find_element(By.CSS_SELECTOR, "td:nth-child(4)").text.strip()
                    index_td += 1
                    continue

                if start_datetime >= exec_datetime:
                    index_download += 1
                    if report_name is None or report_name == report_name_elm:
                        download_count += 1
                        self.click_by_id(
                            "tab0" + str(tab_index) + "_ZZZ000000_BtnDownload_" + str(index_download) + "_9_button")
                        self.wait_page_loaded()
                        time.sleep(1)
                        self.file_download()
                        self.click_by_id("tab0" + str(tab_index) + "_ZACF000300_btnClose")
                        if report_name == report_name_elm:
                            is_found_report_name = True
                            break
                else:
                    is_not_execute_time = True
                    break

                index_td += 1

            if is_not_execute_time: break
            if is_found_report_name: break
            # Click next page
            nextPageBtn = self.find_element_by_xpath(
                '//*[@id="tab0' + str(tab_index) + '_ZZZ000000_TblNohimbutsu_aftBtn_button"]')
            if nextPageBtn.is_displayed():
                nextPageBtn.click()
                self.wait_page_loaded()

        if (download_count > 0):
            # 少し待たないと一時ファイルのままになっている。
            time.sleep(1)
            store = WebRingsSiteTestSnapShotInfo.get_store()

            # 一時フォルダから名前を変更して移動
            for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
                file_base_name = os.path.basename(file_path)
                # print(file_base_name)
                save_file_path = self.__get_download_file_path(file_base_name, case_name)
                shutil.move(file_path, save_file_path)
                store.add_download_file(save_file_path, self)
        return download_count

    def file_download(self):
        try:
            self.driver.switch_to.frame(1)
            self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
            time.sleep(2)
            self.driver.switch_to.default_content()
        except Exception as e:
            self.driver.switch_to.default_content()
            print(e)
            raise Exception("ファイルダウンロードエラー")

    def select_by_id(self, idstr, text=None, value=None):
        """指定されたID属性を持つSELECTに対して指定された値のオプションを選択する"""
        WebDriverHandleHelper.select_by_id(self.driver, idstr, text=text, value=value)
        
    def alert_ok(self):
        """AlertダイアログでOKボタンをクリック"""
        self.wait_page_loaded(wait_timeout=10)
        return WebDriverHandleHelper.alert_ok(self.driver)

    def alert_cancel(self):
        """Alertダイアログで取消ボタンをクリック"""
        return WebDriverHandleHelper.alert_cancel(self.driver)
    
    def alert_ok_many(self):
        """連続で出るAlertダイアログでOKボタンをクリック。戻りはアラートのテキストのlist"""
        return WebDriverHandleHelper.alert_ok_many(self.driver)

    def form_input_by_id(self, idstr, text=None, value=None):
        """Form系の指定されたID属性を持つ要素に対して値を入力する(input/checkbox/radio/textarea/select)"""
        if (self.has_elements_by_css_selector(f"#{idstr}")):
            elm = self.find_element_by_id(idstr)
            if elm.tag_name == "input":
                input_type = elm.get_attribute("type")
                if input_type in (["radio", "checkbox"]):
                    if value :
                        if (value == "1"):
                            if (not elm.is_selected()):
                                elm.click()
                        elif (value == "0"):
                            if (elm.is_selected()):
                                elm.click()
                        else:
                            elm.click()
                elif input_type == "text":
                    if elm.get_attribute("readonly") is None:
                        WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value=value)
                        self.driver.find_element(By.TAG_NAME, "body").click()
                else:
                    WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value=value)
            elif elm.tag_name == "select":
                if elm.is_enabled():
                    if text == "":
                        options = elm.find_elements(By.TAG_NAME, "option")
                        has_empty_option = any(opt.get_attribute("text") == "" for opt in options)
                        if has_empty_option:
                            WebDriverHandleHelper.select_by_id(self.driver, idstr, text="")
                    else:
                        WebDriverHandleHelper.select_by_id(self.driver, idstr, text=text)
                    self.wait_page_loaded(wait_timeout=10)
            elif elm.tag_name == "textarea":
                WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value)
            else:
                WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value=value)

    def has_elements_by_css_selector(self, css_str):
        """要素が存在するかチェック"""
        return WebDriverHandleHelper.has_elements_by_css_selector(self.driver, css_str)

    def fukushi_setai_entry_helper(self, kankei_text="",gaitou_ymd="",sonota_sakujo_flg=True,sonota_kankei_text=""):
        """指定された内容で福祉世帯の入力を行う"""
        kankei_cmb_list = WebDriverHandleHelper.find_elements_by_css_selector(self.driver, 'select[id*="JukyuCmb"]')
        if len(kankei_cmb_list) == 1:
            WebDriverHandleHelper.send_keys_by_id(self.driver, "GaitoYMDtxt_1", value=gaitou_ymd)
        if len(kankei_cmb_list) >= 2:
            WebDriverHandleHelper.send_keys_by_id(self.driver, "GaitoYMDtxt_1", value=gaitou_ymd)
            WebDriverHandleHelper.send_keys_by_id(self.driver, "GaitoYMDtxt_2", value=gaitou_ymd)
            WebDriverHandleHelper.select_by_id(self.driver, "JukyuCmb_2", text=kankei_text)
        if len(kankei_cmb_list) > 2:
            for i in range(3, len(kankei_cmb_list)+1, 1):
                if sonota_sakujo_flg:
                    WebDriverHandleHelper.click_by_id(self.driver, f"ChkFlg_{i}")
                else:
                    WebDriverHandleHelper.send_keys_by_id(self.driver, f"GaitoYMDtxt_{i}", value=gaitou_ymd)
                    WebDriverHandleHelper.select_by_id(self.driver, f"JukyuCmb_{i}", text=sonota_kankei_text)

    def save_screenshot_migrate(self, dummy, file_name, dummy_flg):
        """現行からの移行用"""
        self.screen_shot(file_name)

    def expandDiv(self, elmDiv):
        try:
            scroll_height = self.driver.execute_script("return arguments[0].scrollHeight;", elmDiv)
            div_height = self.driver.execute_script("return arguments[0].offsetHeight;", elmDiv)
        except Exception as e:
            print("!!!GET WINDOWS SIZE ERROR!!!", str(e))

        # スクロール非表示になるために、divの高さを調整
        if div_height < scroll_height - 10:
            style_height = f"{scroll_height}px"
            self.driver.execute_script("arguments[0].style.height = arguments[1]", elmDiv, style_height)
            self.expandedElements.append({'elm':elmDiv,'height':div_height})

    def setDivHeight(self, elm):
        try:
            # self.driver.execute_script("arguments[0].style.height = '[1]px';",elm['elm'],elm['height'])
            self.driver.execute_script("arguments[0].style.height = 'auto';",elm['elm'])
        except Exception as e:
            pass

    def traverseDivs(self, all_divs):
        for div in all_divs:
            child_divs = div.find_elements(By.XPATH, "./div")
            if child_divs: 
                self.traverseDivs(child_divs)

            self.expandDiv(div)        

    def resizeDivs(self):
        for div in self.expandedElements:
            self.setDivHeight(div)

    def screen_shot(self, file_name, caption="", fullsize=False, sysdate_hide=True, sysuser_hide=False):
        """スクリーンショットを取得する"""
        self.wait_page_loaded()
        # print(file_name)
        #self.driver.set_window_size(1450, 768)
        self.driver.set_window_rect(width=int(1550), height=int(768))
        temp_file_path = self.__get_temp_screen_shot_file_path(file_name)
        snap_file_path = self.__get_screen_shot_file_path(file_name)
        if caption == "":
            caption = file_name
        # 福祉系システムのヘッダ行の時間表示を消す
        #tmp_els = WebDriverHandleHelper.find_elements_by_class_no_wait(self.driver, "wr_header1")
        #if len(tmp_els) > 0:
        #    if sysdate_hide:
        #        sl = "#Header1 > tbody > tr > td:nth-child(3) > span:nth-child(1)"
        #        elm = WebDriverHandleHelper.find_element_by_css_selector(self.driver, sl)
        #        self.driver.execute_script("arguments[0].style.visibility = 'hidden';", elm)
        #    if sysuser_hide:
        #        sl = "#Header1 > tbody > tr > td:nth-child(3) > span:nth-child(2)"
        #        elm = WebDriverHandleHelper.find_element_by_css_selector(self.driver, sl)
        #        self.driver.execute_script("arguments[0].style.visibility = 'hidden';", elm)
        # try:
        
        # expanded = False
        # if (CommonSettings.get_settings().is_headless) :
        #     try:
        #         if len(self.expandedElements) > 0 :
        #             self.driver.execute_script("document.body.style.overflow = 'hidden';")
        #             self.resizeDivs()
        #             self.expandedElements.clear()
        #             self.driver.set_window_size(1366, 768)
        #         all_divs = self.find_elements_by_css_selector("#center")
        #     except:
        #         all_divs = []
        #     if len(all_divs) > 0:                
        #         # div展開
        #         self.traverseDivs(all_divs)
        #         total_width  = self.driver.execute_script("return document.body.scrollWidth")
        #         total_height = self.driver.execute_script("return document.body.scrollHeight")
        #         self.driver.execute_script("document.body.style.overflow = 'auto';")
        #         self.driver.set_window_size(total_width, total_height)
        #         expanded = True

        # WebDriverHandleHelper.save_screenshot(self.driver, temp_file_path, fullsize)
        # store = WebRingsSiteTestSnapShotInfo.get_store()
        # store.add_screen_shot_snap(temp_file_path, snap_file_path, caption, self)

        # if (expanded) :
        #     self.driver.set_window_size(total_width, total_height)
        
        if (CommonSettings.get_settings().is_headless) :
            try:
                all_divs = self.find_elements_by_css_selector("#center")
            except:
                print("ポップアップ")
            self.driver.execute_script("document.body.style.overflow = 'hidden';")
            self.resizeDivs()
            self.expandedElements.clear()
            self.driver.set_window_size(1550, 768)
            # div展開
            try:
                self.traverseDivs(all_divs)
            except:
                print("ポップアップ")
            total_width  = self.driver.execute_script("return document.body.scrollWidth")
            total_height = self.driver.execute_script("return document.body.scrollHeight")

            if total_width == 3000:
                total_width = 1550
                total_height = 1200

            # IE用にページサイズがスクリーンサイズ以下の場合に補正
            #スクリーンサイズ取得（フルスクリーン前提）
            screen_width = 1550
            if total_width < screen_width:
                total_width = screen_width
                
            screen_height = 768
            if total_height < screen_height:
                total_height = screen_height
         
            self.driver.execute_script("document.body.style.overflow = 'auto';")
            self.driver.set_window_size(total_width, total_height+150)

        WebDriverHandleHelper.save_screenshot(self.driver, temp_file_path, fullsize)
        store = WebRingsSiteTestSnapShotInfo.get_store()
        store.add_screen_shot_snap(temp_file_path, snap_file_path, caption, self)


    def kojin_kensaku_ichiran_contents_expand(self):
        """個人検索一覧の中のスクロールを拡張する"""
        WebDriverHandleHelper.expand_scroll_dom(self.driver, "#_wrFk_TitleTable_Div_1")

    def jukyu_joukyou_contents_expand(self):
        """受給状況の中のスクロールを拡張する"""
        WebDriverHandleHelper.expand_scroll_dom(self.driver, "#_wrFk_TitleTable_Div_1")

    def shikaku_rireki_contents_expand(self):
        """資格履歴の中のスクロールを拡張する"""
        WebDriverHandleHelper.expand_scroll_dom(self.driver, "div:has(>#ShinseiTable)")

    def return_click(self):
        """戻るボタンをクリックする"""
        WebDriverHandleHelper.click_by_id(self.driver, "GOBACK")

    def shinsei_shikaku_kanri_click(self):
        """メインメニューの申請資格管理ボタンをクリックする"""
        WebDriverHandleHelper.click_by_id(self.driver, "CmdProcess1_1")
        
    def find_common_buttons(self):
        """共通情報ボタンの表示名を元にボタンを要素を取得する"""
        return WebDriverHandleHelper.find_elements_by_css_selector(self.driver, "[id*=btnCommon]")

    def open_common_buttons_area(self):
        """共通情報ボタンが閉じている場合に開く"""
        if self.is_common_button_row_hide():
            el = self.find_common_buttons_open_button()
            el.click()

    def find_common_buttons_open_button(self):
        # return WebDriverHandleHelper.find_element_by_css_selector(self.driver, "a.NOLOCK:has(>img[name=\"name\"])")
        return WebDriverHandleHelper.find_element_by_css_selector(self.driver, "a.NOLOCK:has(>img)")

    def is_common_button_row_hide(self):
        elm = self.find_element_by_id("QAZC002HideRow")
        return not elm.is_displayed()

    def common_button_click(self, button_text):
        """共通情報ボタンの表示名を元にボタンを要素をクリックする"""
        common_button = None
        for v in self.find_elements_by_css_selector("button"):
            if button_text == v.text.strip():
                common_button = v
                break
        if common_button is None:
            raise NoSuchElementException(msg=f"{button_text} のボタンが見つかりませんでした。")
        common_button.click()

    def exec_online_print(self, case_name=""):
        """オンライン帳票の印刷ボタンをクリックして、PDFを所定ディレクトリに保存する"""
        WebDriverHandleHelper.click_by_id_and_alert_ok(self.driver, "InsatsuBtn")
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)
        # # 少し待たないと一時ファイルのままになっている。
        # self.wait_page_loaded(wait_timeout=10)

        # store = WebRingsSiteTestSnapShotInfo.get_store()
        # # 一時フォルダから名前を変更して移動
        # for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
        #     file_base_name = os.path.basename(file_path)
        #     save_file_path = self.__get_download_file_path(file_base_name, case_name)
        #     shutil.move(file_path, save_file_path)
        #     store.add_download_file(save_file_path, self)

    def exec_shintatsu_touroku(self, case_name="", assert_msg=""):
        """進達登録時のPDFを所定ディレクトリに保存する"""
        alert_msg = WebDriverHandleHelper.click_by_id_and_alert_ok(self.driver, "TourokuBtn")
        if(assert_msg != ""):
            self.assertEqual(assert_msg, alert_msg)
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def exec_qsn_online_print(self, case_name=""):
        """QSN画面の印刷ボタンをクリックして、PDFを所定ディレクトリに保存する"""
        WebDriverHandleHelper.click_by_id_and_alert_ok(self.driver, "CmdPrint")
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def __online_print_post_proccess(self, case_name=""):
        # 少し待たないと一時ファイルのままになっている。
        self.wait_page_loaded(wait_timeout=10)

        store = WebRingsSiteTestSnapShotInfo.get_store()
        # 一時フォルダから名前を変更して移動
        for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
            file_base_name = os.path.basename(file_path)
            save_file_path = self.__get_download_file_path(file_base_name, case_name)
            shutil.move(file_path, save_file_path)
            store.add_download_file(save_file_path, self)


    def __get_download_file_path(self, file_name, case_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        return os.path.join(self.settings.dl_dir, full_class_name + "-" + case_name + "-" + file_name)

    def __get_temp_screen_shot_file_path(self, file_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        # 拡張子付いて無かったらpngを固定でつける
        if os.path.splitext(file_name)[1] == "":
            file_name = file_name + ".png"
        return os.path.join(self.settings.snap_work_dir, full_class_name + "-" + file_name)

    def __get_screen_shot_file_path(self, file_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        # 拡張子付いて無かったらpngを固定でつける
        if os.path.splitext(file_name)[1] == "":
            file_name = file_name + ".png"
        return os.path.join(self.settings.snap_dir, full_class_name + "-" + file_name)

    def __get_dom_span_shot_file_path(self, file_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        file_name = file_name + ".txt"
        return os.path.join(self.settings.dom_snap_work_dir, full_class_name + "-" + file_name)

    def __get_old_dom_span_shot_file_path(self, file_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        file_name = file_name + ".txt"
        return os.path.join(self.settings.dom_snap_dir, full_class_name + "-" + file_name)
    
    def help_page_form_items(self, name):
        ret = []
        ret.append("tag,id,name,class\n")
        for el in self.find_elements_by_css_selector("input"):
            ret.append(",".join(["input", str(el.get_attribute("id")), str(el.get_attribute("name")), str(el.get_attribute("class_val")),"\n"]))

        for el in self.find_elements_by_css_selector("select"):
            ret.append(",".join(["select", str(el.get_attribute("id")), str(el.get_attribute("name")), str(el.get_attribute("class_val")),"\n"]))

        for el in self.find_elements_by_css_selector("button"):
            ret.append(",".join(["button", str(el.get_attribute("id")), str(el.get_attribute("name")), str(el.get_attribute("class_val")),"\n"]))
        
        file_path = os.path.join(self.settings.tmp_work_dir, name + ".csv")
        with open(file_path, mode="w", encoding="utf_8_sig") as f:
            f.writelines(ret)

    def assert_message_area(self, msg_span_id, msg):
        """更新後のメッセージエリアの内容をアサートする"""
        ret = False
        msgspan = self.find_element_by_id(msg_span_id) 
        child_divs = msgspan.find_elements(By.XPATH, "./div")
        if child_divs:
            for v in child_divs:
                if msg in v.text.strip():
                    ret = True
                    break
        else:
            if msg in msgspan.text.strip():
                ret = True
        self.assertTrue(ret,msg)

    def assert_alert_text(self, msg):
        """Aleartのメッセージ内容でAssertする"""
        ret = False
        alert_text = WebDriverHandleHelper.alert_text(self.driver)
        self.assertEqual(msg, alert_text)

    def assert_message_base_header(self, msg):
        """基盤側のメッセージ領域の内容をAssertする"""
        
        ret = False
        for v in self.find_elements_by_css_selector("span.wr_message"):
            if msg in v.text:
                ret = True
        self.assertTrue(ret)

    def assert_job_normal_end(self, exec_datetime):
        """ジョブの実行履歴から指定された実行日時付近の実行結果を確認"""
        base_datetime = exec_datetime - datetime.timedelta(seconds=3)  # 3秒前
        self.wait_job_finished()
        self.click_job_exec_log_search()
        is_asserted = False
        list_tbody_elem = None
        for v in self.find_elements_by_css_selector("th.wr_table_title"):
            if "状態" == v.text.strip():
                list_tbody_elem = v.find_element(By.XPATH, "../..")
                break
        for v in list_tbody_elem.find_elements(By.XPATH, "./tr"):
            is_target_row = False
            for elem in v.find_elements(By.XPATH, "./td[5]"):
                start_datetime = datetime.datetime.strptime(elem.text.strip(), "%Y/%m/%d %H:%M:%S")
                if(start_datetime >= base_datetime):
                    is_target_row = True
            if(is_target_row):
                for x in v.find_elements(By.XPATH, "./td[2]"):
                    self.assertEqual("正常終了", x.text.strip())
                    is_asserted = True
        
        self.assertTrue(is_asserted)
        


    def assert_dom_is_difference(self, dom, case_name):
        """指定された要素のHTMLテキストのスナップショットを取得し、前回との差異をアサートする"""
        file_path = self.__get_dom_span_shot_file_path(case_name)
        old_file_path = self.__get_old_dom_span_shot_file_path(case_name)

        html_text = dom.get_attribute("outerHTML")
        with open(file_path, mode='w', encoding="utf-8") as f:
            f.write(html_text)
        
        old_html_text = None
        if os.path.exists(old_file_path):
            # スナップショット更新する場合は古いファイルを上書きしておく
            if self.settings.is_dom_snap_update:
                shutil.copy(file_path, old_file_path)
            with open(old_file_path, mode="r", encoding="utf-8") as f:
                old_html_text = f.read()
        
        if old_html_text is None:
            old_html_text = html_text

        self.assertEqual(old_html_text, html_text)
        
        store = WebRingsSiteTestSnapShotInfo.get_store()
        store.add_dom_snap(file_path, old_file_path, self)

    def assert_upload_file_success(self):
        try:
            message_element = self.find_element_by_id("MSG")
            raise Exception(message_element.text)
        except TimeoutException:
            pass

    def set_job_param_kodomo(self, job_params, tab_index=1):
        for v in job_params:
            self.param_nyuuryoku(v["title"], v["type"], v["value"], tab_index)
            
    def param_nyuuryoku(self, title: str, param_type: str, value, tab_index):
        try:
            table = self.driver.find_element(By.ID, "tab0" + str(tab_index) + "_ZEAF002200_tblParam")
            ui_elements = table.find_elements(By.CSS_SELECTOR, "div.ui-element")

            for elem in ui_elements:
                try:
                    label_span = elem.find_element(By.CSS_SELECTOR, ".label_wrapper span:nth-of-type(2)")
                    label_text = label_span.text.strip()

                    if label_text == title:
                        if param_type == "select":
                            select_elem = elem.find_element(By.TAG_NAME, "select")
                            if value == "":
                                options = select_elem.find_elements(By.TAG_NAME, "option")
                                has_empty_option = any(opt.get_attribute("text") == "" for opt in options)
                                if has_empty_option:
                                    WebDriverHandleHelper.select_option(select_elem, text=value)
                            else:
                                WebDriverHandleHelper.select_option(select_elem, text=value)
                            return

                        elif param_type == "text":
                            input_elem = elem.find_element(By.TAG_NAME, "input")
                            input_elem.clear()
                            input_elem.send_keys(value)
                            input_elem.send_keys(Keys.TAB)
                            return

                        elif param_type == "radio":
                            # Todo
                            return
                        elif param_type == "checkbox":
                            container = elem.find_element(By.CLASS_NAME, "ui_check_wrapper")
                            if not value:
                                return
                            inputs = container.find_elements(By.XPATH, ".//input[@type='checkbox']")
                            labels = container.find_elements(By.XPATH, ".//label")

                            for checkbox, label in zip(inputs, labels):
                                label_text = label.text.strip()
                                if label_text in value:
                                    if not checkbox.is_selected():
                                        checkbox.click()
                            return
                        else:
                            raise ValueError(f"対応していないパラメータータイプです: {param_type}")

                except NoSuchElementException:
                    continue

            raise Exception(f"テーブルに「{title}」というタイトルが見つかりません。")

        except NoSuchElementException:
            raise Exception("ID「tab0" + str(tab_index) + "_ZEAF002200_tblParam」のテーブルが見つかりません。")

    def pdf_download(self,pdfPath,screen_shot_name=""):
        self.driver.set_window_size(1600, 1200)
        try:
            tabId = self.find_element_by_xpath('//*[@title="納品物確認"]').get_attribute('id')
            pages = self.find_element_by_xpath('//*[@id="tab' + tabId + '_ZZZ000000_TblNohimbutsu_spanTotalPage"]').text
            for page in range(int(pages)):
                matchedInPage = False
                spans = self.find_elements_by_css_selector("span.ui_wrlabel_text")
                for v in spans:
                    if pdfPath == v.text.strip():
                        list_button_elem = v.find_element(By.XPATH, "../../../td[6]/div[2]/button")
                        list_button_elem.click()
                        matchedInPage = True
                        break
                if matchedInPage:
                    break
                else:
                    nextPageBtn = self.find_element_by_xpath('//*[@id="tab' + tabId + '_ZZZ000000_TblNohimbutsu_aftBtn_button"]')
                    if nextPageBtn.is_displayed():
                        nextPageBtn.click()
                        self.wait_page_loaded()

            self.wait_page_loaded()
            time.sleep(1)
            self.driver.switch_to.frame(1)            
            self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
            time.sleep(2)
            self.driver.switch_to.default_content()
        except Exception as e:
            self.driver.switch_to.default_content()
            print(e)
            raise Exception("PDFファイルダウンロードエラー")
            
        if screen_shot_name != "":
            self.screen_shot(screen_shot_name)

    def _goto_menu_by_label(self, menu_level_1=None , menu_level_2=None , menu_level_3=None, is_new_tab=False):
        old_window_list = self.driver.window_handles[:]

        def is_submenu_displayed(label_text):
            try:
                label_elem = self.driver.find_element(By.XPATH, f"//span[normalize-space(text())='{label_text}']")
                parent_li = label_elem.find_element(By.XPATH, "./ancestor::li[1]")
                ul_elem = parent_li.find_element(By.XPATH, "./ul")
                return ul_elem.value_of_css_property("display") == "block"
            except Exception:
                return False

        # Open menu_level_1 if not already open
        if menu_level_1 and not is_submenu_displayed(menu_level_1):
            self.click_by_label(menu_level_1)

        # Open menu_level_2 if not already open
        if menu_level_3:
            if not is_submenu_displayed(menu_level_2):
                self.click_by_label(menu_level_2)
            self.double_click_by_label(menu_level_3)
        else:
            self.double_click_by_label(menu_level_2)

        # 現在表示したタブのハンドルに切り替え
        if is_new_tab:
            wait = WebDriverWait(self.driver, 10)
            wait.until(EC.number_of_windows_to_be(len(old_window_list) + 1))
            for window_handle in reversed(self.driver.window_handles):
                if window_handle not in old_window_list:
                    self.driver.close()
                    self.driver.switch_to.window(window_handle)
                    break
            self.wait_page_loaded(wait_timeout=10)

    def click_by_label(self, label):
        """指定されたラベルのボタンをクリックする"""
        elem = None
        for v in self.find_elements_by_css_selector("span"):
            if label == v.text.strip():
                elem = v
                break
        if elem is None:
            raise NoSuchElementException(msg=f"{label} のボタンが見つかりませんでした。")
        elem.click()
        self.wait_page_loaded(wait_timeout=10)

    def double_click_by_label(self, label):
        """指定されたラベルのボタンをクリックする"""
        elem = None
        for v in self.find_elements_by_css_selector("a"):
            if label == v.text.strip():
                elem = v
                break
        if elem is None:
            raise NoSuchElementException(msg=f"{label} のボタンが見つかりませんでした。")
        actions = ActionChains(self.driver)
        actions.double_click(elem).perform()
        self.wait_page_loaded()

    def alert_accept(self):
        xpath = f'//input[@type="button" and @value="{"はい"}"]'
        element = self.driver.find_element(By.XPATH, xpath)
        element.click()
        self.wait_page_loaded(wait_timeout=10)

    def alert_reject(self):
        xpath = f'//input[@type="button" and @value="{"いいえ"}"]'
        element = self.driver.find_element(By.XPATH, xpath)
        element.click()
        self.wait_page_loaded(wait_timeout=10)

    def click_breadcrumb_by_label(self, tab_index, screen_id, label):
        breadcrumbs = self.driver.find_elements(By.CSS_SELECTOR, f"#tab0{tab_index}_{screen_id}_navi li")
        for li in breadcrumbs:
            try:
                a_tag = li.find_element(By.TAG_NAME, "a")
                if a_tag.text.strip() == label:
                    a_tag.click()
                    return
            except:
                pass

            if li.text.strip() == label:
                li.click()
                return
        if len(breadcrumbs) == 0:
            raise NoSuchElementException(msg=f"{label} のボタンが見つかりませんでした。")

    def click_kodomo_by_atena_code(self, kodomo_atena_code, tab_index=1):
        tr_elem = self.find_elements_by_css_selector("#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF1003001 > tbody > tr")
        atena_code_col = "2"
        table_idx = 0
        td_elem_no = None
        td_elem_atena = None
        text_no = ""
        text_atena = ""
        for elem in tr_elem:
            table_idx += 1
            if table_idx == 1:
                td_elem_no = elem.find_element(By.CSS_SELECTOR, "td.ui-valign-middle.ui_wrtable_border_left")
            if table_idx == 2:
                td_elem_atena = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_code_col + ")")
            if table_idx == 3:
                table_idx = 0
            if td_elem_no is not None:
                text_no = td_elem_no.text
            if td_elem_atena is not None:
                text_atena = td_elem_atena.text
            if kodomo_atena_code == text_atena:
                self.click_by_id("tab0" + str(tab_index) + "_ZZZ000000_btnJIDoIchiranRemban_" + str(text_no) + "_1_button")
                break

    def click_setaiin_by_atena_code(self, atena_code, tab_index = 1):
        tr_elem = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "ZZZ000000_tblQAPF1003004 > tbody > tr")
        atena_code_col = "2"
        for elem in tr_elem:
            td_elem = elem.find_element(By.CSS_SELECTOR,
                                        "td:nth-child(" + atena_code_col + ")")
            if atena_code == td_elem.text:
                td_elem_first = elem.find_element(By.CSS_SELECTOR,
                                                  "td.ui-valign-middle.ui_wrtable_border_left")
                self.click_by_id(
                    "#tab0" + str(tab_index) + "ZZZ000000_btnSetainIchiranRemban_" + str(
                        td_elem_first.text) + "_1_button")
                break

    def change_tab_by_label(self, tab_id, screen_id, label):
        tabs = self.find_elements_by_css_selector(f"#tab0{tab_id}_{screen_id}_workguide li")
        for tab in tabs:
            try:
                span = tab.find_element(By.CSS_SELECTOR, 'a.link span')
                if span.text.strip() == label:
                    span.click()
            except:
                continue
        if len(tabs) == 0:
            raise NoSuchElementException(
                msg=f"{label} のボタンが見つかりませんでした。")

    def upload_list_file(self, list_file_path=None, screen_shot_name=None, tab_index=1):
        tr_elem_file = self.find_elements_by_css_selector(f"#tab{tab_index:02d}_ZEAF002800_tblShoriINFO > tbody > tr")
        index_file_upload = 1
        for elem in tr_elem_file:
            td_elem_button_upload = elem.find_element(By.CSS_SELECTOR, "td:nth-child(3)")
            if td_elem_button_upload.text.strip() == "アップロード":
                if len(list_file_path) > index_file_upload - 1:
                    self.click_by_id(f"tab{tab_index:02d}_ZEAF002800_BtnUpload_{index_file_upload}_3_button")
                    self.upload_file_by_path(file_path=list_file_path[index_file_upload - 1],
                                             screen_shot_name=f"{screen_shot_name}_{index_file_upload}")
                else:
                    break
                index_file_upload += 1
        return index_file_upload

    def upload_file_by_path(self, file_path = None, screen_shot_name = None):
        iframe = self.find_element_by_xpath("//iframe[@id='ifr_file' and contains(@style, 'height')]")
        self.driver.switch_to.frame(iframe)

        if not os.path.isfile(file_path):
            raise Exception("ファイルのパスが存在しません。")
        else:
            # 「ファイルの選択」ボタン押下
            upload_input = self.find_element_by_id("UPLOAD1")
            upload_input.send_keys(file_path)
            # 実行指示 画面:「追加」ボタン押下
            self.click_by_id("ADD")
            self.screen_shot(screen_shot_name)
            # 実行指示 画面:「送信」ボタン押下
            self.click_by_id("CONTROL_BUTTON")
            self.assert_upload_file_success()
            self.driver.switch_to.default_content()

    def set_print_param_kodomo(self, label_text=None, value=None, tab_index=1, index_report=0, screen_id=""):
        # Find the currently displayed
        section = self.find_element(By.ID, "tab0" + str(tab_index) + "_" + screen_id + "_section" + str(index_report))

        # Find a specific label in the currently displayed section
        label = section.find_element(By.XPATH, f".//span[normalize-space(text())='{label_text}']")
        # Find parent div containing label and input/select/textarea
        parent_div = label.find_element(By.XPATH, "./ancestor::div[contains(@class, 'ui-element')]")

        # Find input
        input_elements = parent_div.find_elements(By.XPATH, ".//input[not(@type='hidden')]")
        if input_elements:
            self.form_input_by_id(idstr=input_elements[0].get_attribute("id"), value=value)
            return

        # Find select
        select_elements = parent_div.find_elements(By.TAG_NAME, "select")
        if select_elements:
            self.form_input_by_id(idstr=select_elements[0].get_attribute("id"), text=value)
            return

        # Find textarea
        textarea_elements = parent_div.find_elements(By.TAG_NAME, "textarea")
        if textarea_elements:
            self.form_input_by_id(idstr=textarea_elements[0].get_attribute("id"), value=value)
            return
        return "Don't find input by label!"

    def get_kodomo_reports_info(self, tab_index=1, screen_id=""):
        ret = []
        report_tr_list = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "_" + screen_id + "_printsetting > table > tbody > tr")
        for v in report_tr_list:
            report_name = v.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text.strip()
            ret.append(report_name)
        return ret

    def select_kodomo_report(self, case_name="", report_param_list=None, tab_index=1, screen_shot_name=None,
                             screen_id=""):
        reports_list = self.get_kodomo_reports_info(tab_index=tab_index, screen_id=screen_id)
        checked_reports_count = 0
        index_report = 0
        for v in reports_list:
            for p in report_param_list:
                if p["report_name"] == v:
                    self.form_input_by_id(
                        idstr="tab0" + str(tab_index) + "_" + screen_id + "_baseCheckBox" + str(
                            index_report) + "chk0",
                        value="1")
                    for x in p["params_1"]:
                        if x["title"] == "発行場所":
                            self.form_input_by_id(
                                idstr="tab0" + str(tab_index) + "_" + screen_id + "_basePrinterSelect" + str(
                                    index_report) + "_select",
                                text=x["value"])
                        if x["title"] == "トレイ":
                            self.form_input_by_id(
                                idstr="tab0" + str(tab_index) + "_" + screen_id + "_baseTraySelect" + str(
                                    index_report) + "_select",
                                text=x["value"])
                        if x["title"] == "部数":
                            self.form_input_by_id(
                                idstr="tab0" + str(tab_index) + "_" + screen_id + "_basePageText" + str(
                                    index_report) + "_textboxInput",
                                value=x["value"])
                    id_tab_report = "#tab0" + str(tab_index) + "_" + screen_id + "_guide" + str(index_report)
                    try:
                        elm_tab_report = self.find_element(By.ID, id_tab_report)
                        if elm_tab_report.is_displayed() and elm_tab_report.is_enabled():
                            elm_tab_report.click()
                            for y in p["params_2"]:
                                self.set_print_param_kodomo(label_text=y["title"], value=y["value"], tab_index=tab_index,
                                                            index_report=index_report, screen_id=screen_id)
                    except Exception:
                        pass
                    self.screen_shot(screen_shot_name + "_" + v)
                    checked_reports_count += 1
            index_report += 1

        return checked_reports_count

    def print_kodomo_reports(self, case_name="", limit_wait_count=10, time_span_sec=3, tab_index=1,
                             screen_shot_name="", count_report=1, report_name=None):
        startTime = datetime.datetime.now()
        self.wait_page_loaded(wait_timeout=time_span_sec)
        waitTime = datetime.datetime.now() - startTime
        while waitTime.seconds < limit_wait_count:
            report_tr_list = self.find_elements_by_css_selector(
                "#tab0" + str(tab_index) + "_ZZZ000000_tblPrintIchiran > tbody > tr")
            if len(report_tr_list) - 1 >= count_report:
                # Get all status
                statuses = [
                    th.find_element(By.CSS_SELECTOR, "td:nth-child(5)").text.strip()
                    for th in report_tr_list
                ]

                # Check all status diff "実行中"
                if all(status != "実行中" for status in statuses):
                    self.screen_shot(screen_shot_name)
                    break
            self.wait_page_loaded(wait_timeout=time_span_sec)
            waitTime = datetime.datetime.now() - startTime

        # Download file
        report_tr_list = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "_ZZZ000000_tblPrintIchiran > tbody > tr")
        index_report = 0
        for th in report_tr_list:
            report_name_th = th.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text.strip()
            report_status = th.find_element(By.CSS_SELECTOR, "td:nth-child(5)").text.strip()
            if report_status == "正常終了":
                if report_name is None or report_name == report_name_th:
                    try:
                        WebDriverWait(self.driver, limit_wait_count).until(
                            EC.invisibility_of_element_located((By.ID, "progressArea"))
                        )
                    except TimeoutException:
                        print(
                            f"[ERROR] Loading bar with ID 'progressArea' still visible after {limit_wait_count} seconds.")
                    self.click_button_by_label(str(index_report))
                    self.wait_page_loaded(wait_timeout=time_span_sec)
                    time.sleep(2)
            index_report += 1

        # Save file
        self.save_kodomo_reports(case_name=case_name)

    def save_kodomo_reports(self, case_name=""):
        WebDriverHandleHelper.wait_download(self.driver)
        # 少し待たないと一時ファイルのままになっている。
        time.sleep(1)

        store = WebRingsSiteTestSnapShotInfo.get_store()
        # 一時フォルダから名前を変更して移動
        for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
            file_base_name = os.path.basename(file_path)
            save_file_path = self.__get_download_file_path(file_base_name, case_name)
            shutil.move(file_path, save_file_path)
            store.add_download_file(save_file_path, self)
        self.wait_page_loaded(wait_timeout=3)

    def select_teishutsu_shorui_kodomo(self, document_param_list=None, tab_index=1):
        document_tr_list = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "_ZZZ000000_tblQAZF0038001 > tbody > tr")
        for p in document_param_list:
            for tr in document_tr_list:
                if tr.text:
                    document_name_1 = tr.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text.strip()
                    document_name_2 = tr.find_element(By.CSS_SELECTOR, "td:nth-child(5)").text.strip()
                    if document_name_1 == p["document_name"]:
                        checkbox = tr.find_element(By.CSS_SELECTOR, "td.ui-valign-middle.ui_wrtable_border_left input[type='checkbox']")
                        if not checkbox.is_selected():
                            checkbox.click()
                            time.sleep(1)

                        date_input = tr.find_element(By.CSS_SELECTOR, "td:nth-child(3) input.textbox_input")
                        date_input.clear()
                        date_input.send_keys(next((x["value"] for x in p["params"] if x["title"] == "提出日"), ""))
                        time.sleep(1)
                        break
                    if document_name_2 == p["document_name"]:
                        checkbox = tr.find_element(By.CSS_SELECTOR, "td:nth-child(4) input[type='checkbox']")
                        if not checkbox.is_selected():
                            checkbox.click()
                            time.sleep(1)

                        date_input = tr.find_element(By.CSS_SELECTOR, "td:nth-child(6) input.textbox_input")
                        date_input.clear()
                        date_input.send_keys(next((x["value"] for x in p["params"] if x["title"] == "提出日"), ""))
                        time.sleep(1)
                        break

    def click_shikyuu_nintei_rireki_by_atena_code(self, kodomo_atena_code=None, tab_index=1):
        tr_elem = self.find_elements_by_css_selector("#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF1003003 > tbody > tr")
        for elem in tr_elem:
            if elem.text:
                text_no = elem.find_element(By.CSS_SELECTOR, "td.ui-valign-middle.ui_wrtable_border_left").text
                if elem.find_element(By.CSS_SELECTOR, "td:nth-child(6)").text == kodomo_atena_code:
                    self.click_by_id(
                        "tab0" + str(tab_index) + "_ZZZ000000_btnShikyuNinteiRirekiRemban_" + str(text_no) + "_1_button")
                    break
        
    def get_navigation_info(self, step_index):
        """
        Gets the name of the last li in breadcrumb and the name of the tab which li has selected_li class.
        
        Returns:
            tuple: (last_breadcrumb_item, selected_tab_name)
        """
        try:
            # Get breadcrumb navigation
            tab = self.find_element_by_css_selector(f"div[role='tabpanel'].ui-tabs-panel.ui-widget-content.ui-corner-bottom[style*='display: block']")
            breadcrumb_navi = tab.find_element(By.CSS_SELECTOR, "div.navi_area.pankuzulist")
            breadcrumb_items = breadcrumb_navi.find_elements(By.TAG_NAME, "li")
            # Get the last breadcrumb item
            last_breadcrumb_item = ""
            if breadcrumb_items:
                last_li = breadcrumb_items[-1]
                # Check if it has a link (a tag) or just text
                try: 
                    link_elements = last_li.find_elements(By.TAG_NAME, "a")
                    last_breadcrumb_item = link_elements[0].text.strip()
                except Exception as e:
                    last_breadcrumb_item = last_li.text.strip()
            else: 
              return "", ""
            
            # Get tab navigation
            selected_tab_name = ""
            
            if(self.has_tab_guide(tab=tab)):
                tab_navi = tab.find_element(By.CSS_SELECTOR, ".tab_guide")
                tab_items = tab_navi.find_elements(By.TAG_NAME, "li")
                
                # Find the tab with selected_li class
                for tab_item in tab_items:
                    if "selected_li" in tab_item.get_attribute("class"):
                        # Get the span text inside the tab
                        span_elements = tab_item.find_elements(By.TAG_NAME, "span")
                        for span in span_elements:
                            text = span.text.strip()
                            if text and text != "●":
                                selected_tab_name = text
                                break
                        break
            
            self.logger.info(f"Last breadcrumb item: {last_breadcrumb_item}")
            self.logger.info(f"Selected tab: {selected_tab_name}")
            
            return last_breadcrumb_item, selected_tab_name
            
        except Exception as e:
            self.logger.error(f"Error getting navigation info: {str(e)}")
            return "", ""

    def has_tab_guide(self, tab=None):
        """
        Checks if the .tab_guide element exists on the current page.
        
        Returns:
            bool: True if .tab_guide element exists, False otherwise
        """
        try:
            tab_guide = self.find_element_by_css_selector(".tab_guide") if tab is None else tab.find_element(By.CSS_SELECTOR, ".tab_guide")
            exists = tab_guide is not None
            self.logger.info(f"Tab guide element exists: {exists}")
            return exists
        except Exception as e:
            self.logger.info(f"Tab guide element not found: {str(e)}")
            return False

    def auto_screen_shot(self, step_index):
        last_breadcrumb_item, selected_tab_name = self.get_navigation_info(step_index)
        while(last_breadcrumb_item == ""): 
          last_breadcrumb_item, selected_tab_name = self.get_navigation_info(step_index)
        if(selected_tab_name == ""):
            self.screen_shot(file_name=f"{last_breadcrumb_item}画面_{step_index}")
        else:
            self.screen_shot(file_name=f"{last_breadcrumb_item}画面_{selected_tab_name}_{step_index}")
      
    def click_input_type_button_by_value(self, value):
        """指定されたラベルのボタンをクリックする"""
        button_elem = None
        for v in self.find_elements_by_css_selector('input[type="button"]'):
            if value == v.get_attribute("value"):
                button_elem = v
                break
        if button_elem is None:
            raise NoSuchElementException(msg=f"{value} のボタンが見つかりませんでした。")
        button_elem.click()

    def click_ninka_jigyousha_by_jigyousha_bangou(self, jigyousha_bangou="", tab_index=1):
        if (self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAPF201100_WrTableIchiran")):
            pages = self.find_element_by_xpath(
                '//*[@id="tab0' + str(tab_index) + '_QAPF201100_pages1"]').text
            index_jigyousha_bangou = 1
            for page in range(int(pages)):
                tr_elem = self.find_elements_by_css_selector(
                    "#tab0" + str(tab_index) + "_QAPF201100_WrTableIchiran > table > tbody > tr")
                for elem in tr_elem:
                    td_elem_jigyousha_bangou = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text
                    if jigyousha_bangou == td_elem_jigyousha_bangou:
                        self.click_input_type_button_by_value(str(index_jigyousha_bangou))
                        return
                    index_jigyousha_bangou += 1

                # Click next page
                nextPageBtn = self.find_element_by_xpath(
                    '//*[@id="tab0' + str(tab_index) + '_QAPF201100_WrButton053_button"]')
                if nextPageBtn.is_displayed():
                    nextPageBtn.click()
                    self.wait_page_loaded(wait_timeout=10)

    def click_ninka_jigyousho_by_jigyousho_bangou(self, jigyousho_bangou="", tab_index=1):
        if (self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAPF201300_WrTableIchiran")):
            pages = self.find_element_by_xpath(
                '//*[@id="tab0' + str(tab_index) + '_QAPF201300_pages1"]').text
            index_jigyousho_bangou = 1
            for page in range(int(pages)):
                tr_elem = self.find_elements_by_css_selector(
                    "#tab0" + str(tab_index) + "_QAPF201300_WrTableIchiran > table > tbody > tr")
                for elem in tr_elem:
                    td_elem_jigyousho_bangou = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text
                    if jigyousho_bangou == td_elem_jigyousho_bangou:
                        self.click_input_type_button_by_value(str(index_jigyousho_bangou))
                        return
                    index_jigyousho_bangou += 1

                # Click next page
                nextPageBtn = self.find_element_by_xpath(
                    '//*[@id="tab0' + str(tab_index) + '_QAPF201300_WrButton053_button"]')
                if nextPageBtn.is_displayed():
                    nextPageBtn.click()
                    self.wait_page_loaded(wait_timeout=10)

    def click_gaitou_shisetsu_by_shisetsu_koudo(self, shisetsu_koudo="", tab_index=1):
        if (self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAPF201700_WrTableIchiran")):
            pages = self.find_element_by_xpath(
                '//*[@id="tab0' + str(tab_index) + '_QAPF201700_pages1"]').text
            index_shisetsu_koudo = 1
            for page in range(int(pages)):
                tr_elem = self.find_elements_by_css_selector(
                    "#tab0" + str(tab_index) + "_QAPF201700_WrTableIchiran > table > tbody > tr")
                for elem in tr_elem:
                    td_elem_shisetsu_koudo = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text
                    if shisetsu_koudo == td_elem_shisetsu_koudo:
                        self.click_input_type_button_by_value(str(index_shisetsu_koudo))
                        return
                    index_shisetsu_koudo += 1

                # Click next page
                nextPageBtn = self.find_element_by_xpath(
                    '//*[@id="tab0' + str(tab_index) + '_QAPF201700_WrButton053_button"]')
                if nextPageBtn.is_displayed():
                    nextPageBtn.click()
                    self.wait_page_loaded(wait_timeout=10)

    def select_kodomo_saabisu_shurui(self, service_params=None, tab_index=1):
        service_list = []
        service_tr_list = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF2004001 > tbody > tr")
        for s in service_tr_list:
            s_name = s.find_element(By.CSS_SELECTOR, "td:nth-child(4)").text.strip()
            service_list.append(s_name)

        checked_service_count = 0
        index_service = 0
        for param in service_params:
            for service_name in service_list:
                if param["service_name"] == service_name:
                    for x in param["params"]:
                        if x["title"] == "使用":
                            self.form_input_by_id(
                                idstr="tab0" + str(tab_index) + "_ZZZ000000_chkServiceShiyo_" + str(
                                    index_service) + "_3chk0",
                                value=x["value"])
                        if x["title"] == "独自料金":
                            self.form_input_by_id(
                                idstr="tab0" + str(tab_index) + "_ZZZ000000_chkDokujiRyokin_" + str(
                                    index_service) + "_5chk0",
                                value=x["value"])
                        if x["title"] == "月額(上限額)":
                            self.form_input_by_id(
                                idstr="tab0" + str(tab_index) + "_ZZZ000000_txtGetugaku_" + str(
                                    index_service) + "_6_textboxInput",
                                value=x["value"])
                        if x["title"] == "単位当たり額":
                            self.form_input_by_id(
                                idstr="tab0" + str(tab_index) + "_ZZZ000000_txtTaniAtariGaku_" + str(
                                    index_service) + "_7_textboxInput",
                                value=x["value"])
                        if x["title"] == "実績行割当":
                            self.form_input_by_id(
                                idstr="tab0" + str(tab_index) + "_ZZZ000000_selJiseikigyo_" + str(
                                    index_service) + "_8_select",
                                text=x["value"])
                    checked_service_count += 1
                index_service += 1

        return checked_service_count

    def select_no_kobetsu_keshikomirow_by_tsuuchisho_bangou(self, tsuuchisho_bangou="", tab_index=1):
        tr_elem = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "_ZZZ000000_tblJAAF403400List > tbody > tr")
        for elem in tr_elem:
            try:
                td_elem_no = elem.find_element(By.CSS_SELECTOR, "td.ui-valign-middle.ui_wrtable_border_left")
                td_elem_tsuuchisho_bangou = elem.find_element(By.CSS_SELECTOR, "td:nth-child(5)")
                if tsuuchisho_bangou == td_elem_tsuuchisho_bangou.text.strip():
                    self.click_button_by_label(td_elem_no.text.strip())
                    return
            except:
                continue

    def select_atena_code_after_relative_search(self, atena_code="", tab_index="1", col_number=1):
        screen_id = self.find_element_by_id("screenIdSpan").text.strip()
        # ページ数を取得
        page_info = self.find_element_by_id(f"tab0{tab_index}_{screen_id}_pages1")
        total_pages = int(page_info.text.strip())  # 例: "5" -> 5ページ
        found_row = None

        # 全ページをループ
        for page in range(1, total_pages + 1):
            # 対象のテーブルを取得
            table = self.driver.find_element(By.ID, f"tab0{tab_index}_{screen_id}_WrTableIchiran")
            rows = table.find_elements(By.CSS_SELECTOR, "table.ui_wrtable_table tbody tr")

            # 各行をループ
            for row in rows:
                cols = row.find_elements(By.TAG_NAME, "td")
                if len(cols) > 1:
                    # 2列目のテキストを取得
                    td_text = cols[col_number].text.strip()
                    # Atenaコードと一致するか確認
                    if atena_code in td_text:
                        found_row = row
                        # 1列目のボタンをクリック
                        btn = cols[0].find_element(By.TAG_NAME, "input")
                        btn.click()
                        self.wait_page_loaded(wait_timeout=10)
                        return found_row  # 見つかったら処理終了

            # 最後のページでなければ「次へ」ボタンをクリック
            if page < total_pages:
                self.click_by_id(f"tab0{tab_index}_{screen_id}_WrButton053_button")

        if not found_row:
            print(f"{atena_code} は {total_pages} ページに存在しません。")

        return None
    
    def select_ruiseki_fuicchi_ichiran_by_atena_code_and_fuichi_kubun(self, atena_code, fuichi_kubun, tab_index):
            # ページ数を取得
            page_info = self.find_element_by_id(f"tab0{tab_index}_ZZZ000000_tblJAAF301800List_spanTotalPage")
            total_pages = int(page_info.text.strip())  # 例: "5" -> 5ページ
            found_row = None

            # 全ページをループ
            for page in range(1, total_pages + 1):
                # 対象のテーブルを取得
                table = self.driver.find_element(By.ID, f"tab0{tab_index}_ZZZ000000_tblJAAF301800List")
                rows = table.find_elements(By.CSS_SELECTOR, "tbody tr")
                # 各行をループ
                for row in rows:
                    cols = row.find_elements(By.TAG_NAME, "td")
                    if len(cols) > 1:
                        # 2列目のテキストを取得
                        td_atena = cols[5].text.strip()
                        try: 
                            td_fuichi = cols[10].text.strip()
                        except:
                            continue
                        # Atenaコードと一致するか確認
                        if atena_code in td_atena and any(k in td_fuichi for k in fuichi_kubun):
                            found_row = row
                            # 1列目のボタンをクリック
                            btn = cols[12].find_element(By.TAG_NAME, "button")
                            btn.click()
                            self.wait_page_loaded(wait_timeout=10)
                            return found_row  # 見つかったら処理終了
                        
                # 最後のページでなければ「次へ」ボタンをクリック
                if page < total_pages:
                    self.click_by_id(f"tab0{tab_index}_ZZZ000000_tblJAAF301800List_aftBtn_button")

            if not found_row:
                print(f"{atena_code} は {total_pages} ページに存在しません。")

            return None
        