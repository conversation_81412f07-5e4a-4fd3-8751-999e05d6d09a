import copy
import datetime
import glob
import json
import logging
import shutil
import os
import unittest
import time
from urllib.parse import urlparse
from urllib.parse import urljoin

from base.driver import WebRingsSiteTestDriver
from base.snap_store import WebRingsSiteTestSnapShotInfo
from util.helper import WebDriverHandleHelper
from util.config import CommonSettings
from util.dbutil import SQLHandler
from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains

from util.config import CommonSettings
__settings = CommonSettings.get_settings()
__logger = logging.getLogger("fukushi.test.log")
__logger.setLevel(logging.DEBUG)
__handler = logging.FileHandler(filename=os.path.join(__settings.log_dir, "fukushi-test.log"), mode="w")
__formatter = logging.Formatter('%(asctime)s - %(levelname)s:%(name)s - %(message)s')
__handler.setFormatter(__formatter)
__logger.addHandler(__handler)

debug_script1 = """
var target_elem = document.getElementById('$$ID_TEXT$$');
var bounds = target_elem.getBoundingClientRect();

var body_elem = document.getElementsByTagName('body')[0]
var new_elem = document.createElement("div");
var style_str = 'background-color: #ccc;position: absolute;z-index: 999999;top:'+bounds.top + 'px;left: '+bounds.left + 'px'
new_elem.appendChild(document.createTextNode('$$ID_TEXT$$'));
new_elem.setAttribute('style',style_str);
new_elem.setAttribute('data-debug','1');
body_elem.appendChild(new_elem);
"""

debug_delete_script1 = """
const elm = document.querySelector('div[data-debug]');
if (0 < elm.length) {
    [...elm].forEach(function(v){return v.remove()});
}
"""

class FukushiSiteTestCaseBase(unittest.TestCase):

    env_name = "fukushi"
    settings = None
    settings_dict = {}
    test_data_root_path = ""
    test_sql_root_path = ""
    test_data = {}
    common_test_data = {}
    test_sql = ""
    jichitai_code = ""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 最初にドライバーの初期化入るとおかしくなるので無し setup内でやる
        # self.driver = WebRingsSiteTestDriver.get_driver()
        self.driver = None
        self.settings = CommonSettings.get_settings()
        self.settings_dict = self.settings.settings_json.get(self.env_name, {})
        self.logger = logging.getLogger("fukushi.test.log")
        self.db_keys = self.__get_db_keys(self.settings_dict)
    
    @classmethod
    def setUpClass(cls):
        cls.settings = CommonSettings.get_settings()
        cls.settings_dict = cls.settings.settings_json.get(cls.env_name, {})
        cls.jishitai_code = cls.settings_dict.get("jichitai_code","")
        cls.test_data_root_path = os.path.join(cls.__get_test_root_path(),"testdata")
        cls.test_sql_root_path = os.path.join(cls.__get_test_root_path(),"testsql")
        cls.db_keys = cls.__get_db_keys(cls.settings_dict)
        # テストクラスに対応するデータ
        data_path = cls.__get_test_data_path()
        if(os.path.exists(data_path)):
            with open(data_path, mode="r", encoding="utf-8") as f:
                cls.test_data = json.load(f)
        # testdataフォルダに存在する共通テストデータ
        data_path = cls.__get_common_test_data_path()
        if(os.path.exists(data_path)):
            with open(data_path, mode="r", encoding="utf-8") as f:
                cls.common_test_data = json.load(f)
        
    @classmethod
    def tearDownClass(cls):
        pass
    
    @classmethod
    def __get_db_keys(cls, prm_dict):
        ret = []
        for k in prm_dict.keys():
            if(type(prm_dict[k]) == dict):
                if(not prm_dict[k].get("host",None) is None):
                    ret.append(k)
        return ret
    
    def exec_sqlfile(self, file_name, params=None, db_key=""):
        test_sql_path = os.path.join(self.get_current_sql_dir(), file_name)
        test_sql_text = ""
        if(os.path.exists(test_sql_path)):
            for k in self.db_keys:
                if(db_key == "" or db_key==k):
                    print("SQL実行", "DBkey", k, test_sql_path)
                    _db = self.get_db(db_key=k)
                    _db.exec_sqlfile(test_sql_path, params=params)
        else:
            print(f"指定されたSQLファイルが存在しません。: {test_sql_path}")

    def setUp(self):
        # Driverの初期化処理
        self.driver = WebRingsSiteTestDriver.get_driver()
        self.accept_next_alert = True
        test_sql_path = self.get_current_setup_sql_path()
        test_sql_text = ""
        if(os.path.exists(test_sql_path)):
            for k in self.db_keys:
                print("SQL実行", test_sql_path)
                _db = self.get_db(db_key=k)
                _db.exec_sqlfile(test_sql_path)

    def tearDown(self):
        test_sql_path = self.get_current_teardown_sql_path()
        test_sql_text = ""
        if(os.path.exists(test_sql_path)):
            for k in self.db_keys:
                print("SQL実行", test_sql_path)
                _db = self.get_db(db_key=k)
                _db.exec_sqlfile(test_sql_path)
    
    def get_db(self, env_name="", db_key="db"):
        db_param = self.get_dbparams(env_name=env_name, db_key=db_key)
        sql_handler = SQLHandler(
            p_jichitai_code=self.jishitai_code,
            p_server=db_param.get("host",""),
            p_db="master",
            p_user=db_param.get("user_id",""),
            p_pass=db_param.get("password",""),
            p_catalogs=db_param.get("catalogs",""),
        )
        return sql_handler

    def get_dbparams(self, env_name="", db_key="db"):
        if env_name == "":
            return self.settings_dict.get(db_key,{})
        else:
            settings = self.settings.settings_json.get(self.env_name, {})
            return settings.get(db_key,{})
        
    @classmethod
    def __get_test_root_path(cls):
        settings = CommonSettings.get_settings()
        module_paths = cls.get_current_module_name().split(".")
        return os.path.join(settings.src_dir,module_paths[0],module_paths[1])

    @classmethod
    def __get_test_data_path(cls):
        settings = CommonSettings.get_settings()
        module_paths = cls.get_current_module_name().split(".")
        return os.path.join(cls.test_data_root_path, module_paths[-1]+".json")

    @classmethod
    def __get_common_test_data_path(cls):
        settings = CommonSettings.get_settings()
        module_paths = cls.get_current_module_name().split(".")
        return os.path.join(cls.test_data_root_path, "common_test_data.json")

    @classmethod
    def get_current_module_name(cls):
        return cls.__module__

    @classmethod
    def get_current_class_name(cls):
        return cls.__name__
    
    @classmethod
    def get_current_class_full_name(cls):
        return ".".join([cls.__module__, cls.__name__])
    
    def get_current_method_name(self):
        return self.id().split(".")[-1]

    def get_current_sql_dir(self):
        self.test_sql_root_path
        module_paths = self.get_current_module_name().split(".")
        return os.path.join(self.test_sql_root_path)

    def get_current_setup_sql_path(self):
        self.test_sql_root_path
        module_paths = self.get_current_module_name().split(".")
        return os.path.join(self.test_sql_root_path, module_paths[-1], self.get_current_method_name()+"-setup.sql")

    def get_current_teardown_sql_path(self):
        self.test_sql_root_path
        module_paths = self.get_current_module_name().split(".")
        return os.path.join(self.test_sql_root_path, module_paths[-1], self.get_current_method_name()+"-teardown.sql")

    def do_login(self, user_id=None, password=None):
        """ログイン→メインメニューの遷移"""
        if user_id is None or password is None:
            user_id = self.settings_dict.get("user_id","")
            password = self.settings_dict.get("password","")
        
        login_url = urljoin(self.settings_dict.get("base_url",""), self.settings_dict.get("login_url",""))
        self.get(login_url)
        self.wait_page_loaded()
        self.send_keys_by_name(self.settings_dict.get("user_id_element",""), user_id)
        self.send_keys_by_name(self.settings_dict.get("password_element",""), password)
        self.click_by_name(self.settings_dict.get("login_element",""))
        self.click_by_id(self.settings_dict.get("fukushi_btn_element",""))
        
    def goto_jukyu_joukyou(self, atena_code, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→受給状況の遷移"""
        self.do_login(user_id=user_id, password=password)
        self.shinsei_shikaku_kanri_click()
        self.send_keys_by_id("AtenaCD", atena_code)
        self.click_by_id("Kensaku")
    
    def kojin_kensaku_by_atena_code(self, atena_code):
        """指定された宛名コードで個人検索"""
        self.send_keys_by_id("AtenaCD", atena_code)
        self.click_by_id("Kensaku")

    def click_jukyujoukyou_by_gyoumu_code(self, gyoumu_code=""):
        """受給状況画面の各業務ボタンを業務コード指定でクリックする"""
        gyoumu_button = None
        for v in self.find_elements_by_css_selector("button"):
            if gyoumu_code in v.get_attribute("id"):
                gyoumu_button = v
                break
        if gyoumu_button is None:
            raise NoSuchElementException(msg=f"{gyoumu_code} のボタンが見つかりませんでした。")
        gyoumu_button.click()

    def goto_jukyu_joukyou_and_click_gyoumu(self, atena_code, gyoumu_code, user_id=None, password=None):
        """指定された宛名コードでログイン→メインメニュー→受給状況→指定された業務コードの資格管理画面への遷移"""
        self.goto_jukyu_joukyou(atena_code=atena_code, user_id=user_id, password=password)
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code=gyoumu_code)
    
    def click_rireki_select(self, num=1):
        """履歴選択画面の場合にnumで指定された資格履歴のボタンをクリックする。0の場合は新規資格登録ボタンをクリックする。履歴選択画面ではない場合は何もしない。遷移動作をした場合はTrueを戻値で返す。何もしなかった場合はFalse。"""
        ret = False
        if("QAZF031.aspx" in self.driver.current_url):
            if(num==0):
                ret = True
                self.click_by_id("CmdShinkiShikakuToroku")
            else:
                ret = True
                self.click_by_id(f"CmdButton1_{num}")
        return ret

    def fukushi_file_upload_qazf210(self, upload_file_path):
        """イメージアップロード画面でのファイルアップロード"""
        self.driver.execute_script("return FileUpload()")
        self.find_element_by_id("_wr_upload_files").send_keys(upload_file_path)
        self.driver.execute_script("return document.getElementById('_wr_upload_files').dispatchEvent(new Event('change', { 'bubbles': true }))")
        
    def fukushi_folder_upload_qazf210(self, upload_file_path):
        """イメージアップロード画面でのフォルダアップロード"""
        self.driver.execute_script("return FolderUpload()")
        self.find_element_by_id("_wr_upload_folder").send_keys(upload_file_path)
        self.driver.execute_script("return document.getElementById('_wr_upload_folder').dispatchEvent(new Event('change', { 'bubbles': true }))")
        
    def fukushi_folder_upload_qsnf002(self, upload_file_path):
        """福祉収納の連携ファイル転送画面でのフォルダアップロード"""
        self.driver.execute_script("return UploadProc()")
        self.find_element_by_id("_wr_upload_folder").send_keys(upload_file_path)
        self.driver.execute_script("return document.getElementById('_wr_upload_folder').dispatchEvent(new Event('change', { 'bubbles': true }))")
        
    def wait_page_loaded(self, wait_timeout=10):
        """ページが完全に読み込まれるまで待機する"""
        WebDriverHandleHelper.wait_page_loaded(self.driver, wait_timeout)

    def get(self, url):
        return WebDriverHandleHelper.get(self.driver, url)

    def find_element(self, method_type, value):
        """既存移行用"""
        if(method_type == By.ID):
            return self.find_element_by_id(value)
        elif(method_type ==  By.NAME):
            return self.find_element_by_name(value)
        elif(method_type ==  By.XPATH):
            return self.find_element_by_xpath(value)
        else:
            return self.find_element_by_css_selector(value)

    def select_Option(self, dummy, elem, label):
        WebDriverHandleHelper.select_option(elem, text=label)

    def find_element_by_name(self, name):
        """指定されたNAME属性を持つ要素を取得"""
        return WebDriverHandleHelper.find_element_by_name(self.driver, name)

    def find_element_by_id(self, idstr):
        """指定されたID属性を持つ要素を取得"""
        return WebDriverHandleHelper.find_element_by_id(self.driver, idstr)
        
    def find_element_by_xpath(self, xpath):
        """指定されたXPATHの要素を取得"""
        return WebDriverHandleHelper.find_element_by_xpath(self.driver, xpath)
        
    def find_element_by_css_selector(self, selector):
        """指定されたCSSセレクタに該当する最初の要素を取得"""
        return WebDriverHandleHelper.find_element_by_css_selector(self.driver, selector)

    def find_elements_by_css_selector(self, selector):
        """指定されたCSSセレクタに該当する全要素を取得(要素のListを返却)"""
        return WebDriverHandleHelper.find_elements_by_css_selector(self.driver, selector)

    def find_element_with_web_element_by_css_selector(self, el, selector):
        """指定されたCSSセレクタに該当する最初の要素を取得"""
        return WebDriverHandleHelper.find_element_with_web_element_by_css_selector(self.driver, el, selector)

    def find_elements_with_web_element_by_css_selector(self, el, selector):
        """指定されたCSSセレクタに該当する全要素を取得(要素のListを返却)"""
        return WebDriverHandleHelper.find_elements_with_web_element_by_css_selector(self.driver, el, selector)

    def find_element_by_tag_name(self, name):
        """指定されたTAG名の要素の内最初に取得出来たものを返却"""
        return WebDriverHandleHelper.find_element_by_tag_name(self.driver, name)

    def send_keys_by_name(self, name, value):
        """指定されたNAME属性を持つ要素にvalueのキーボード入力を行う"""
        WebDriverHandleHelper.send_keys_by_name(self.driver, name, value)

    def send_keys_by_id(self, idstr, value):
        """指定されたID属性を持つ要素にvalueのキーボード入力を行う"""
        WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value)

    def click_by_name(self, name):
        """指定されたNAME属性を持つ要素でClickを行う"""
        WebDriverHandleHelper.click_by_name(self.driver, name)
        
    def click_by_id(self, idstr):
        """指定されたID属性を持つ要素でClickを行う"""
        WebDriverHandleHelper.click_by_id(self.driver, idstr)

    def click_by_id_and_ok(self, idstr):
        """指定されたID属性を持つ要素でClickを行いアラートOKする"""
        WebDriverHandleHelper.click_by_id_and_ok(self.driver, idstr)

    def click_by_id_and_cansel(self, idstr):
        """指定されたID属性を持つ要素でClickを行いアラートキャンセルする"""
        WebDriverHandleHelper.click_by_id_and_cansel(self.driver, idstr)

    def click_button_by_label(self, label):
        """指定されたラベルのボタンをクリックする"""
        ret = False
        button_elem = None
        for v in self.find_elements_by_css_selector("button"):
            if label == v.text.strip():
                button_elem = v
                break
        if button_elem is None:
            # raise NoSuchElementException(msg=f"{label} のボタンが見つかりませんでした。")
            ret = False
        else:
            ret = True
            button_elem.click()
        return ret

    def click_input_button_by_label(self, label):
        """指定されたラベルのINPUTボタンをクリックする"""
        ret = False
        button_elem = None
        for v in self.find_elements_by_css_selector("input"):
            if label == v.get_attribute("value").strip():
                button_elem = v
                break
        if button_elem is None:
            # raise NoSuchElementException(msg=f"{label} のボタンが見つかりませんでした。")
            ret = False
        else:
            ret = True
            button_elem.click()
        return ret

    def click_by_id_and_new_tab(self, id_str):
        """指定されたIDでボタンクリック後、新しく開いたタブに制御を移す"""
        old_window_list = self.driver.window_handles[:]
        elm = self.click_by_id(id_str)
        # 現在表示したタブのハンドルに切り替え
        wait = WebDriverWait(self.driver, 10)
        wait.until(EC.number_of_windows_to_be(len(old_window_list)+1))
        for window_handle in reversed(self.driver.window_handles):
            if window_handle not in old_window_list:
                self.driver.switch_to.window(window_handle)
                break

    def click_batch_job_button_by_label(self, job_label):
        job_button_elem = None
        for v in self.find_elements_by_css_selector("td.wr_Css_table_LB"):
            if job_label == v.text.strip():
                job_button_elem = v.find_element(By.XPATH, '../td/button')
                break
        if job_button_elem is not None:
            job_button_elem.click()
    
    def execute_script(self, script_text):
        """指定されたScriptの実行"""
        WebDriverHandleHelper.execute_script(self.driver, script_text)

    def set_job_params(self, job_params):
        for v in job_params:
            self.set_job_param(v["title"], v["type"], v["value"])

    def set_job_param(self, title, param_type, value):
        for v in self.find_elements_by_css_selector("td.wr_Css_table_LB"):
            if title == v.text.strip():
                if param_type == "text":
                    param_input = v.find_element(By.XPATH, '../td/input')
                    param_input.click()
                    param_input.send_keys("")
                    param_input.send_keys(value)
                if param_type == "select":
                    param_input = v.find_element(By.XPATH, '../td/select')
                    WebDriverHandleHelper.select_option(param_input, text=value)
                if param_type == "check":
                    param_input = v.find_element(By.XPATH, './input')
                    id_value = param_input.get_attribute("id")
                    id_value = id_value.replace("check","item")
                    input_elem = self.find_element_by_id(id_value)
                    if param_input.is_selected():
                        if value == "0":
                            param_input.click()
                            # self.driver.execute_script("arguments[0].setAttribute('value',arguments[1])",input_elem, value)
                    else:
                        if value == "1":
                            param_input.click()
                            # self.driver.execute_script("arguments[0].setAttribute('value',arguments[1])",input_elem, value)
                break
    
    def has_elements_by_css_selector(self, css_str):
        """要素が存在するかチェック"""
        # Seleniumは要素がない場合は例外を出すので、JS直呼び以外はこれでチェックするしかない
        # ヘルパーのを使うと10秒待機してしまうので、driverを直接呼ぶ
        # ret = True
        # script_text = f"return document.querySelector(\"{css_str}\")"
        # try:
        #     script_ret = self.driver.execute_script(script_text)
        #     if script_ret is not None:
        #         ret = True
        #     else:
        #         ret = False
        # except:
        #     ret = False
        # return ret
        return WebDriverHandleHelper.has_elements_by_css_selector(self.driver, css_str)

    def expand_fukushi_normal_table_scroll(self):
        if(self.has_elements_by_css_selector("#_wrFk_TitleTable_Div_1")):
            WebDriverHandleHelper.expand_scroll_dom(self.driver, "#_wrFk_TitleTable_Div_1")

    def exec_batch_job(self):
        """ジョブの実行"""
        exec_button = self.find_element_by_id("ExecuteButton")
        exec_button.click()
        self.alert_ok()
        utc_now = datetime.datetime.utcnow()
        return utc_now + datetime.timedelta(hours=9)

    def click_job_exec_log(self):
        """ジョブの実行履歴ボタンクリック"""
        exec_button = self.find_element_by_id("JobListButton")
        exec_button.click()

    def click_job_exec_log_search(self):
        """ジョブの実行履歴の検索ボタンクリック"""
        exec_button = self.find_element_by_id("SearchButton")
        exec_button.click()

    def click_report_log(self):
        """ジョブの帳票履歴ボタンクリック"""
        exec_button = self.find_element_by_id("ReportListButton")
        exec_button.click()

    def click_job_list(self):
        """ジョブの処理一覧ボタンクリック"""
        exec_button = self.find_element_by_id("ExecListButton")
        exec_button.click()

    def wait_job_finished(self, limit_wait_count=10, time_span_sec=3):
        """ジョブ実行履歴の一覧に処理中が無くなるまで待機"""
        self.click_job_exec_log_search()
        list_tbody_elem = None
        for count in range(limit_wait_count):
            all_done = True
            for v in self.find_elements_by_css_selector("th.wr_table_title"):
                if "状態" == v.text.strip():
                    list_tbody_elem = v.find_element(By.XPATH, "../..")
                    break
            for v in list_tbody_elem.find_elements(By.XPATH, "./tr"):
                for elem in v.find_elements(By.XPATH, "./td[2]"):
                    if elem.text.strip() == "処理中":
                        all_done = False
                        break
            if(all_done):
                break
            time.sleep(time_span_sec)
            self.click_job_exec_log_search()

    def get_job_report_pdf(self, exec_datetime, case_name="", target_report_name=None):
        """実行時間以降に作成されたPDFをDLする"""
        base_datetime = exec_datetime - datetime.timedelta(seconds=3)  # 3秒前
        self.click_job_exec_log_search()
        list_tbody_elem = None
        download_count = 0
        downloaded_list = []
        is_continue = True
        while(is_continue):
            list_tbody_elem = self.__get_batch_reports_root_table()
            tr_count = len(WebDriverHandleHelper.find_elements_with_web_element_by_css_selector(self.driver, list_tbody_elem, "tr"))
            for i in range(tr_count):
                list_tbody_elem = self.__get_batch_reports_root_table()
                tr_rec = WebDriverHandleHelper.find_elements_with_web_element_by_css_selector(self.driver, list_tbody_elem, "tr")[i]
                is_target_row = False
                downloaded_check_key = ""
                wk_datetime_text = ""
                for elem in tr_rec.find_elements(By.XPATH, "./td[5]"):
                    report_name = ""
                    for report_name_elem in tr_rec.find_elements(By.XPATH, "./td[3]"):
                        report_name = report_name_elem.text.strip()
                    if target_report_name is None or target_report_name in report_name:
                        wk_datetime_text = elem.text.strip()
                        downloaded_check_key = f"{report_name}:{wk_datetime_text}"
                        start_datetime = datetime.datetime.strptime(wk_datetime_text, "%Y/%m/%d %H:%M:%S")
                        if (start_datetime >= base_datetime):
                            if (downloaded_check_key not in downloaded_list):
                                is_target_row = True
                if(is_target_row):
                    for x in WebDriverHandleHelper.find_elements_with_web_element_by_css_selector(self.driver, tr_rec, "button"):
                        x.click()
                        download_done = True
                        download_count += 1
                        downloaded_list.append(downloaded_check_key)
                        time.sleep(1)
                        break
                else:
                    is_continue = False

        if(download_count > 0):
            # 少し待たないと一時ファイルのままになっている。
            time.sleep(1)

            store = WebRingsSiteTestSnapShotInfo.get_store()
            
            # 一時フォルダから名前を変更して移動
            for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
                file_base_name = os.path.basename(file_path)
                # print(file_base_name)
                save_file_path = self.__get_download_file_path(file_base_name, case_name)
                shutil.move(file_path, save_file_path)
                store.add_download_file(save_file_path, self)
        return download_count
    
    def __get_batch_reports_root_table(self):
        list_tbody_elem = None
        for v in self.find_elements_by_css_selector("th.wr_table_title"):
            if "開始頁" == v.text.strip():
                list_tbody_elem = v.find_element(By.XPATH, "../..")
                break
        return list_tbody_elem


    def goto_output_files_dl_page(self, exec_datetime):
        """ジョブの実行履歴から指定された実行日時付近の実行結果の作成データのページへ遷移"""
        base_datetime = exec_datetime - datetime.timedelta(seconds=3)  # 3秒前
        self.click_job_exec_log_search()
        is_dl_page = False
        list_tbody_elem = None
        target_dl_page_button = None
        for v in self.find_elements_by_css_selector("th.wr_table_title"):
            if "状態" == v.text.strip():
                list_tbody_elem = v.find_element(By.XPATH, "../..")
                break
        for v in list_tbody_elem.find_elements(By.XPATH, "./tr"):
            is_target_row = False
            for elem in v.find_elements(By.XPATH, "./td[5]"):
                start_datetime = datetime.datetime.strptime(elem.text.strip(), "%Y/%m/%d %H:%M:%S")
                if(start_datetime >= base_datetime):
                    is_target_row = True
            if(is_target_row):
                for x in v.find_elements(By.XPATH, "./td[7]/button"):
                    is_dl_page = True
                    target_dl_page_button = x
                    break
        if(is_dl_page and target_dl_page_button is not None):
            target_dl_page_button.click()
        else:
            is_dl_page = False
        return is_dl_page

    def get_job_output_files(self, case_name=""):
        """ダウンロードページにいる前提で全てのファイルをDL"""
        download_count = 0
        for v in self.find_elements_by_css_selector("button.NOLOCK"):
            if(v.get_attribute("id").strip().startswith("No")):
                download_count += 1
                v.click()
        if(download_count > 0):
            # 少し待たないと一時ファイルのままになっている。
            time.sleep(1)

            store = WebRingsSiteTestSnapShotInfo.get_store()
            # 一時フォルダから名前を変更して移動
            for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
                file_base_name = os.path.basename(file_path)
                save_file_path = self.__get_download_file_path(file_base_name, case_name)
                shutil.move(file_path, save_file_path)
                store.add_download_file(save_file_path, self)
        return download_count

    def select_by_id(self, idstr, text=None, value=None):
        """指定されたID属性を持つSELECTに対して指定された値のオプションを選択する"""
        WebDriverHandleHelper.select_by_id(self.driver, idstr, text=text, value=value)
        
    def select_by_name(self, name_str, text=None, value=None):
        """指定されたNAME属性を持つSELECTに対して指定された値のオプションを選択する"""
        WebDriverHandleHelper.select_by_name(self.driver, name_str, text=text, value=value)
        
    def alert_ok(self):
        """AlertダイアログでOKボタンをクリック"""
        time.sleep(1)
        return WebDriverHandleHelper.alert_ok(self.driver)

    def alert_cancel(self):
        """Alertダイアログで取消ボタンをクリック"""
        return WebDriverHandleHelper.alert_cancel(self.driver)
    
    def alert_ok_many(self):
        """連続で出るAlertダイアログでOKボタンをクリック。戻りはアラートのテキストのlist"""
        return WebDriverHandleHelper.alert_ok_many(self.driver)

    def form_input_by_id(self, idstr, text=None, value=None):
        """Form系の指定されたID属性を持つ要素に対して値を入力する(input/checkbox/radio/textarea/select)"""
        if(self.has_elements_by_css_selector(f"#{idstr}")):
            elm = self.find_element_by_id(idstr)
            if elm.tag_name == "input":
                input_type = elm.get_attribute("type")
                if input_type in (["radio","checkbox"]):
                    if(value=="1"):
                        if(not elm.is_selected()):
                            elm.click()
                    elif(value=="0"):
                        if(elm.is_selected()):
                            elm.click()
                    else:
                        elm.click()
                elif input_type == "text":
                    WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value=value)
                else:
                    WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value=value)
            elif elm.tag_name == "select":
                WebDriverHandleHelper.select_by_id(self.driver, idstr, text=text, value=value)
                self.wait_page_loaded()
            elif elm.tag_name == "textarea":
                WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value)
            else:
                WebDriverHandleHelper.send_keys_by_id(self.driver, idstr, value=value)

    def fukushi_setai_entry_helper(self, kankei_text="",gaitou_ymd="",sonota_sakujo_flg=True,sonota_kankei_text=""):
        """指定された内容で福祉世帯の入力を行う"""
        kankei_cmb_list = WebDriverHandleHelper.find_elements_by_css_selector(self.driver, 'select[id*="JukyuCmb"]')
        if len(kankei_cmb_list) == 1:
            # WebDriverHandleHelper.send_keys_by_id(self.driver, "GaitoYMDtxt_1", value=gaitou_ymd)
            self.form_input_by_id(idstr="GaitoYMDtxt_1", value=gaitou_ymd)
        if len(kankei_cmb_list) >= 2:
            self.form_input_by_id(idstr="GaitoYMDtxt_1", value=gaitou_ymd)
            self.form_input_by_id(idstr="GaitoYMDtxt_2", value=gaitou_ymd)
            self.form_input_by_id(idstr="JukyuCmb_2", text=kankei_text)
            # WebDriverHandleHelper.send_keys_by_id(self.driver, "GaitoYMDtxt_1", value=gaitou_ymd)
            # WebDriverHandleHelper.send_keys_by_id(self.driver, "GaitoYMDtxt_2", value=gaitou_ymd)
            # WebDriverHandleHelper.select_by_id(self.driver, "JukyuCmb_2", text=kankei_text)
        if len(kankei_cmb_list) > 2:
            for i in range(2, len(kankei_cmb_list)+1, 1):
                if sonota_sakujo_flg:
                    self.form_input_by_id(idstr=f"ChkFlg_{i}", value="1")
                    # WebDriverHandleHelper.click_by_id(self.driver, f"ChkFlg_{i}")
                else:
                    self.form_input_by_id(idstr=f"GaitoYMDtxt_{i}", value=gaitou_ymd)
                    self.form_input_by_id(idstr=f"JukyuCmb_{i}", text=sonota_kankei_text)
                    # WebDriverHandleHelper.send_keys_by_id(self.driver, f"GaitoYMDtxt_{i}", value=gaitou_ymd)
                    # WebDriverHandleHelper.select_by_id(self.driver, f"JukyuCmb_{i}", text=sonota_kankei_text)

    def save_screenshot_migrate(self, dummy, file_name, dummy_flg):
        """現行からの移行用"""
        self.screen_shot(file_name)

    def screen_shot_debug(self, file_name):
        """IDデバッグ用"""
        id_list = []
        
        self.logger.info("==================================")
        self.logger.info(file_name)
        self.logger.info("==================================")

        if(self.has_elements_by_css_selector("button")):
            self.logger.info("=== BUTTON ===")
            for v in self.find_elements_by_css_selector("button"):
                tmp_id = v.get_attribute("id")
                id_list.append(tmp_id)
                self.logger.info(tmp_id)
        if(self.has_elements_by_css_selector("input")):
            self.logger.info("=== INPUT ===")
            for v in self.find_elements_by_css_selector("input"):
                if(v.get_attribute("type") != "hidden"):
                    tmp_id = v.get_attribute("id")
                    id_list.append(tmp_id)
                    self.logger.info(tmp_id)
        if(self.has_elements_by_css_selector("select")):
            self.logger.info("=== SELECT ===")
            for v in self.find_elements_by_css_selector("select"):
                tmp_id = v.get_attribute("id")
                id_list.append(tmp_id)
                self.logger.info(tmp_id)
        if(self.has_elements_by_css_selector("textarea")):
            self.logger.info("=== TEXTAREA ===")
            for v in self.find_elements_by_css_selector("textarea"):
                tmp_id = v.get_attribute("id")
                id_list.append(tmp_id)
                self.logger.info(tmp_id)
        
        for id_str in id_list:
            tmp_script = debug_script1.replace("$$ID_TEXT$$", id_str)
            self.driver.execute_script(tmp_script)
        
        self.screen_shot(file_name, sysdate_hide=False)
        self.driver.execute_script(debug_delete_script1)
        

    def screen_shot(self, file_name, caption="", fullsize=True, sysdate_hide=True, sysuser_hide=False, default_expand=True):
        """スクリーンショットを取得する"""
        temp_file_path = self.__get_temp_screen_shot_file_path(file_name)
        snap_file_path = self.__get_screen_shot_file_path(file_name)
        if caption == "":
            caption = file_name
        elm = None
        # 福祉系システムのヘッダ行の時間表示を消す
        tmp_els = WebDriverHandleHelper.find_elements_by_class_no_wait(self.driver, "wr_header1")
        if len(tmp_els) > 0:
            # if sysdate_hide:
            #     sl = "#Header1 > tbody > tr > td:nth-child(3) > span:nth-child(1)"
            #     elm = WebDriverHandleHelper.find_element_by_css_selector(self.driver, sl)
            #     self.driver.execute_script("arguments[0].style.visibility = 'hidden';", elm)
            # if sysuser_hide:
            #     sl = "#Header1 > tbody > tr > td:nth-child(3) > span:nth-child(2)"
            #     elm = WebDriverHandleHelper.find_element_by_css_selector(self.driver, sl)
            #     self.driver.execute_script("arguments[0].style.visibility = 'hidden';", elm)
            if sysdate_hide:
                sl = "#Header1 > tbody > tr > td:nth-child(3)"
                elm = WebDriverHandleHelper.find_element_by_css_selector(self.driver, sl)
                self.driver.execute_script("arguments[0].style.visibility = 'hidden';", elm)
        self.expand_fukushi_normal_table_scroll()
        self.shikaku_rireki_contents_expand()
        
        WebDriverHandleHelper.save_screenshot(self.driver, temp_file_path, fullsize)
        store = WebRingsSiteTestSnapShotInfo.get_store()
        store.add_screen_shot_snap(temp_file_path, snap_file_path, caption, self)
        
        # 非表示にした場合は元に戻す
        if elm is not None and sysdate_hide:
            self.driver.execute_script("arguments[0].style.visibility = 'visible';", elm)

    def kojin_kensaku_ichiran_contents_expand(self):
        """個人検索一覧の中のスクロールを拡張する"""
        WebDriverHandleHelper.expand_scroll_dom(self.driver, "#_wrFk_TitleTable_Div_1")

    def jukyu_joukyou_contents_expand(self):
        """受給状況の中のスクロールを拡張する"""
        WebDriverHandleHelper.expand_scroll_dom(self.driver, "#_wrFk_TitleTable_Div_1")

    def shikaku_rireki_contents_expand(self):
        """資格履歴の中のスクロールを拡張する"""
        if(self.has_elements_by_css_selector("div:has(>#ShinseiTable)")):
            WebDriverHandleHelper.expand_scroll_dom(self.driver, "div:has(>#ShinseiTable)")

    def return_click(self):
        """戻るボタンをクリックする"""
        WebDriverHandleHelper.click_by_id(self.driver, "GOBACK")

    def shinsei_shikaku_kanri_click(self):
        """メインメニューの申請資格管理ボタンをクリックする"""
        button_label = self.settings_dict.get("main_menu_btn_label_shinsei_shikaku","")
        self.click_button_by_label(button_label)

    def batch_kidou_click(self):
        """click on button バッチ起動"""
        button_label = self.settings_dict.get("main_menu_btn_label_batch_kidou","")
        self.click_button_by_label(button_label)

    def shiharai_chousei_click(self):
        """click on button 支払調整"""
        button_label = self.settings_dict.get("main_menu_btn_label_shiharai_chousei","")
        self.click_button_by_label(button_label)

    def saiken_kanri_click(self):
        """click on button 債権管理"""
        button_label = self.settings_dict.get("main_menu_btn_label_saiken_kanri","")
        self.click_button_by_label(button_label) 

    def master_maintenance_click(self):
        """click on button マスタメンテナンス"""
        button_label = self.settings_dict.get("main_menu_btn_label_master_maintenance","")
        self.click_button_by_label(button_label)

    def find_common_buttons(self):
        """共通情報ボタンの全要素を取得する"""
        return WebDriverHandleHelper.find_elements_by_css_selector(self.driver, "[id*=btnCommon]")

    def find_jukyu_joukyou_gyoumu_buttons(self):
        """受給状況の業務ボタン要素を全て取得する"""
        return WebDriverHandleHelper.find_elements_by_css_selector(self.driver, "button.wrFk_CssButton_DF")

    def open_common_buttons_area(self):
        """共通情報ボタンが閉じている場合に開く"""
        if self.is_common_button_row_hide():
            el = self.find_common_buttons_open_button()
            el.click()

    def find_common_buttons_open_button(self):
        # return WebDriverHandleHelper.find_element_by_css_selector(self.driver, "a.NOLOCK:has(>img[name=\"name\"])")
        return WebDriverHandleHelper.find_element_by_css_selector(self.driver, "a.NOLOCK:has(>img)")

    def is_common_button_row_hide(self):
        elm = self.find_element_by_id("QAZC002HideRow")
        return not elm.is_displayed()

    def common_button_click(self, button_text):
        """共通情報ボタンの表示名を元にボタンを要素をクリックする"""
        common_button = None
        for v in self.find_elements_by_css_selector("button"):
            if button_text == v.text.strip():
                common_button = v
                break
        if common_button is None:
            raise NoSuchElementException(msg=f"{button_text} のボタンが見つかりませんでした。")
        common_button.click()

    def pdf_output_and_download(self, button_id, case_name=""):
        """指定されたIDのボタンをクリックして、出力されたPDFを所定ディレクトリに保存する"""
        WebDriverHandleHelper.click_by_id_and_alert_ok(self.driver, button_id)
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def pdf_output_and_download_no_alert(self, button_id, case_name=""):
        """指定されたIDのボタンをクリックして、出力されたPDFを所定ディレクトリに保存する（確認ダイアログなしVer.）"""
        WebDriverHandleHelper.click_by_id(self.driver, button_id)
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def exec_online_print(self, case_name=""):
        """オンライン帳票の印刷ボタンをクリックして、PDFを所定ディレクトリに保存する"""
        WebDriverHandleHelper.click_by_id_and_alert_ok(self.driver, "InsatsuBtn")
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def exec_shintatsu_touroku(self, case_name="", assert_msg=""):
        """進達登録時のPDFを所定ディレクトリに保存する"""
        alert_msg = WebDriverHandleHelper.click_by_id_and_alert_ok(self.driver, "TourokuBtn")
        if(assert_msg != ""):
            self.assertEqual(assert_msg, alert_msg)
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def exec_qsn_online_print(self, case_name=""):
        """QSN画面の印刷ボタンをクリックして、PDFを所定ディレクトリに保存する"""
        WebDriverHandleHelper.click_by_id_and_alert_ok(self.driver, "CmdPrint")
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def exec_RAJF016_online_print(self, case_name=""):
        """イメージファイル印刷画面の印刷ボタンをクリックして、PDFを所定ディレクトリに保存する"""
        WebDriverHandleHelper.click_by_id_and_alert_ok(self.driver, "CmdInsatsu")
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def exec_shinchoku_online_print(self, case_name=""):
        """総合支援進捗管理画面の印刷ボタンをクリックして、PDFを所定ディレクトリに保存する"""
        WebDriverHandleHelper.click_by_id(self.driver, "CmdPrintOut")
        WebDriverHandleHelper.wait_download(self.driver)
        self.__online_print_post_proccess(case_name=case_name)

    def __online_print_post_proccess(self, case_name=""):
        # 少し待たないと一時ファイルのままになっている。
        time.sleep(1)

        store = WebRingsSiteTestSnapShotInfo.get_store()
        # 一時フォルダから名前を変更して移動
        for file_path in glob.glob(self.settings.dl_work_dir + "/*"):
            file_base_name = os.path.basename(file_path)
            save_file_path = self.__get_download_file_path(file_base_name, case_name)
            shutil.move(file_path, save_file_path)
            store.add_download_file(save_file_path, self)


    def __get_download_file_path(self, file_name, case_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        return os.path.join(self.settings.dl_dir, full_class_name + "-" + case_name + "-" + file_name)

    def __get_temp_screen_shot_file_path(self, file_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        # 拡張子付いて無かったらpngを固定でつける
        if os.path.splitext(file_name)[1] == "":
            file_name = file_name + ".png"
        return os.path.join(self.settings.snap_work_dir, full_class_name + "-" + file_name)

    def __get_screen_shot_file_path(self, file_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        # 拡張子付いて無かったらpngを固定でつける
        if os.path.splitext(file_name)[1] == "":
            file_name = file_name + ".png"
        return os.path.join(self.settings.snap_dir, full_class_name + "-" + file_name)

    def __get_dom_span_shot_file_path(self, file_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        file_name = file_name + ".txt"
        return os.path.join(self.settings.dom_snap_work_dir, full_class_name + "-" + file_name)

    def __get_old_dom_span_shot_file_path(self, file_name):
        full_class_name = "-".join(self.__class__.__module__.split('.')) + "-" + self.__class__.__name__
        file_name = file_name + ".txt"
        return os.path.join(self.settings.dom_snap_dir, full_class_name + "-" + file_name)
    
    def help_page_form_items(self, name):
        ret = []
        ret.append("tag,id,name,class\n")
        for el in self.find_elements_by_css_selector("input"):
            ret.append(",".join(["input", str(el.get_attribute("id")), str(el.get_attribute("name")), str(el.get_attribute("class_val")),"\n"]))

        for el in self.find_elements_by_css_selector("select"):
            ret.append(",".join(["select", str(el.get_attribute("id")), str(el.get_attribute("name")), str(el.get_attribute("class_val")),"\n"]))

        for el in self.find_elements_by_css_selector("button"):
            ret.append(",".join(["button", str(el.get_attribute("id")), str(el.get_attribute("name")), str(el.get_attribute("class_val")),"\n"]))
        
        file_path = os.path.join(self.settings.tmp_work_dir, name + ".csv")
        with open(file_path, mode="w", encoding="utf_8_sig") as f:
            f.writelines(ret)

    def assert_message_area(self, msg):
        """資格管理画面などの更新後のメッセージエリアの内容をアサートする"""
        ret = False
        for v in self.find_elements_by_css_selector(".wr_table > td > b"):
            if msg in v.text:
                ret = True
        self.assertTrue(ret)

    def assert_alert_text(self, msg):
        """Aleartのメッセージ内容でAssertする"""
        ret = False
        alert_text = WebDriverHandleHelper.alert_text(self.driver)
        self.assertEqual(msg, alert_text)

    def assert_message_base_header(self, msg):
        """基盤側のメッセージ領域の内容をAssertする"""
        
        ret = False
        for v in self.find_elements_by_css_selector("span.wr_message"):
            if msg in v.text:
                ret = True
        self.assertTrue(ret)

    def assert_job_normal_end(self, exec_datetime):
        """ジョブの実行履歴から指定された実行日時付近の実行結果を確認"""
        base_datetime = exec_datetime - datetime.timedelta(seconds=3)  # 3秒前
        self.wait_job_finished()
        self.click_job_exec_log_search()
        is_asserted = False
        list_tbody_elem = None
        for v in self.find_elements_by_css_selector("th.wr_table_title"):
            if "状態" == v.text.strip():
                list_tbody_elem = v.find_element(By.XPATH, "../..")
                break
        for v in list_tbody_elem.find_elements(By.XPATH, "./tr"):
            is_target_row = False
            for elem in v.find_elements(By.XPATH, "./td[5]"):
                start_datetime = datetime.datetime.strptime(elem.text.strip(), "%Y/%m/%d %H:%M:%S")
                if(start_datetime >= base_datetime):
                    is_target_row = True
            if(is_target_row):
                for x in v.find_elements(By.XPATH, "./td[2]"):
                    self.assertEqual("正常終了", x.text.strip())
                    is_asserted = True
        
        self.assertTrue(is_asserted)

    def assert_dom_is_difference(self, dom, case_name):
        """指定された要素のHTMLテキストのスナップショットを取得し、前回との差異をアサートする"""
        file_path = self.__get_dom_span_shot_file_path(case_name)
        old_file_path = self.__get_old_dom_span_shot_file_path(case_name)

        html_text = dom.get_attribute("outerHTML")
        with open(file_path, mode='w', encoding="utf-8") as f:
            f.write(html_text)
        
        old_html_text = None
        if os.path.exists(old_file_path):
            # スナップショット更新する場合は古いファイルを上書きしておく
            if self.settings.is_dom_snap_update:
                shutil.copy(file_path, old_file_path)
            with open(old_file_path, mode="r", encoding="utf-8") as f:
                old_html_text = f.read()
        
        if old_html_text is None:
            old_html_text = html_text

        self.assertEqual(old_html_text, html_text)
        
        store = WebRingsSiteTestSnapShotInfo.get_store()
        store.add_dom_snap(file_path, old_file_path, self)

    def assert_message_confirm_popup(self, msg):
        """確認ポップアップのメッセージ内容をアサートする"""
        ret = False
        for v in self.find_elements_by_css_selector(".confirm_msg"):
            if msg in v.text:
                ret = True
        self.assertTrue(ret)

    def entry_memo_info(self, text="", input_ymd="", input_hh="", input_mm="", input_tanto="",kbn1="",kbn2="",is_important=False):
        if(input_ymd != ""):
            self.form_input_by_id(idstr="TxtNyuryokuYMD", value=input_ymd)
        if(input_hh != ""):
            self.form_input_by_id(idstr="TxtNyuryokuHH", value=input_hh)
        if(input_mm != ""):
            self.form_input_by_id(idstr="TxtNyuryokuMM", value=input_mm)
        if(text != ""):
            self.form_input_by_id(idstr="TxtNaiyo", value=text)
        if(input_tanto != ""):
            self.form_input_by_id(idstr="TxtNyurkTantoCode", value=input_tanto)
        if(kbn1 != ""):
            self.form_input_by_id(idstr="CmbKubun1", text=kbn1)
        if(kbn2 != ""):
            self.form_input_by_id(idstr="CmbKubun2", text=kbn2)
        if(is_important):
            self.form_input_by_id(idstr="JyuyouChkBox", value="1")
        else:
            self.form_input_by_id(idstr="JyuyouChkBox", value="0")
    
    def entry_honnin_renraku(self, yusen_text="", jitaku="", rusuden=False, keitai="", fax="", kinmu_text="",kinmu_tel="",kinmu_fax="",mail="",koukai=True, etc_list=None, mail2=""):
        if(yusen_text != ""):
            self.form_input_by_id(idstr="CmbYusenTEL", text=yusen_text)
        if(jitaku != ""):
            self.form_input_by_id(idstr="TxtTelJitaku", value=jitaku)
        if(rusuden):
            self.form_input_by_id(idstr="ChkRusuden", value="1")
        else:
            self.form_input_by_id(idstr="ChkRusuden", value="0")
        if(keitai != ""):
            self.form_input_by_id(idstr="TxtTelKeitai", value=keitai)
        if(fax != ""):
            self.form_input_by_id(idstr="TxtFaxJitaku", value=fax)
        if(kinmu_text != ""):
            self.form_input_by_id(idstr="TxtKinmuSaki", value=kinmu_text)
        if(kinmu_tel != ""):
            self.form_input_by_id(idstr="TxtTelKinmu", value=kinmu_tel)
        if(kinmu_fax != ""):
            self.form_input_by_id(idstr="TxtFaxKinmu", value=kinmu_fax)
        if(mail != ""):
            self.form_input_by_id(idstr="TxtMail", value=mail)
        if(mail2 != ""):
            self.form_input_by_id(idstr="TxtMail2", value=mail2)
        if(koukai):
            self.form_input_by_id(idstr="kokai0", value="1")
        else:
            self.form_input_by_id(idstr="kokai1", value="1")
        if(etc_list is not None and type(etc_list) == list):
            item_idx = 1
            for item in etc_list:
                if(len(item)>=3):
                    self.form_input_by_id(idstr=f"SelectHanyo{item_idx}", text=item[0])
                    # 読み込まれるまで下記で待つ
                    self.find_element_by_id(f"TxtHanyoRenraku{item_idx}")
                    self.form_input_by_id(idstr=f"TxtHanyoRenraku{item_idx}", value=item[1])
                    self.form_input_by_id(idstr=f"TxtHanyoBiko{item_idx}", value=item[2])
                    item_idx += 1
    def entry_soufu_saki(self, 
        start_ymd="",
        end_ymd="",
        left_zip="",
        right_zip="",
        shokan_ku_text="",
        jusho_text="",
        katagaki_text="",
        jusho_code="",
        banchi="",
        go_value="",
        go_eda="",
        go_ko_eda="",
        shimei="",
        kana_shimei="",
        koukai=True,
    ):
        if(start_ymd != ""):
            self.form_input_by_id(idstr="TxtStartYMD", value=start_ymd)
        if(end_ymd != ""):
            self.form_input_by_id(idstr="TxtEndYMD", value=end_ymd)
        if(left_zip != ""):
            self.form_input_by_id(idstr="TxtLeftZip", value=left_zip)
        if(right_zip != ""):
            self.form_input_by_id(idstr="TxtRightZip", value=right_zip)
        if(shokan_ku_text != ""):
            self.form_input_by_id(idstr="CmdShokatsu", text=shokan_ku_text)
        if(jusho_text != ""):
            self.form_input_by_id(idstr="TxtJusho", value=jusho_text)
        if(katagaki_text != ""):
            self.form_input_by_id(idstr="TxtKataGaki", value=katagaki_text)
        if(jusho_code != ""):
            self.form_input_by_id(idstr="TxtShinaiJushoCode", value=jusho_code)
        if(banchi != ""):
            self.form_input_by_id(idstr="TxtShinaiBanchi", value=banchi)
        if(go_value != ""):
            self.form_input_by_id(idstr="TxtShinaiGo", value=go_value)
        if(go_eda != ""):
            self.form_input_by_id(idstr="TxtShinaiGoEdaban", value=go_eda)
        if(go_ko_eda != ""):
            self.form_input_by_id(idstr="TxtShinaiGoKoedaban", value=go_ko_eda)
        if(kana_shimei != ""):
            self.form_input_by_id(idstr="TxtkanaShimei", value=kana_shimei)
        if(shimei != ""):
            self.form_input_by_id(idstr="TxtShimei", value=shimei)
        if(koukai):
            self.form_input_by_id(idstr="RadioPublic", value="1")
        else:
            self.form_input_by_id(idstr="RadioLocal", value="1")
    
    def entry_kouza_info(self, 
        start_ymd="", 
        end_ymd="", 
        ginko_code="", 
        shiten_code="", 
        kouza_shubetsu_text="", 
        kouza_bango="",
        meigi_kana="",
        meigi_kanji="",
        koukai=True,
    ):
        if(start_ymd != ""):
            self.form_input_by_id(idstr="口座情報_u_YukoKikanKaishi", value=start_ymd)
        if(end_ymd != ""):
            self.form_input_by_id(idstr="口座情報_u_YukoKikanShuryo", value=end_ymd)
        if(ginko_code != ""):
            self.form_input_by_id(idstr="口座情報_u_KinyuKikanCode", value=ginko_code)
        if(shiten_code != ""):
            self.form_input_by_id(idstr="口座情報_u_ShitenCode", value=shiten_code)
        if(kouza_shubetsu_text != ""):
            self.form_input_by_id(idstr="口座情報_u_KouzaShubetu", text=kouza_shubetsu_text)
        if(kouza_bango != ""):
            self.form_input_by_id(idstr="口座情報_u_KouzaBango", value=kouza_bango)
        if(meigi_kana != ""):
            self.form_input_by_id(idstr="口座情報_u_MeigininKana", value=meigi_kana)
        if(meigi_kanji != ""):
            self.form_input_by_id(idstr="口座情報_u_MeigininKanji", value=meigi_kanji)
        if(koukai):
            self.form_input_by_id(idstr="口座情報_u_KoukaiKubun0", value="1")
        else:
            self.form_input_by_id(idstr="口座情報_u_KoukaiKubun1", value="1")

    def get_teishutsu_shorui_ctrl_info(self):
        ret = {}
        table_idx = 0
        for table_elem in self.find_elements_by_css_selector("table.wr_table"):
            table_idx += 1
            if("提出書類" in table_elem.text):
                for tr_elem in table_elem.find_elements(By.CSS_SELECTOR, "tr:has(>td)"):
                    td1 = tr_elem.find_element(By.CSS_SELECTOR, "td:nth-child(1)")
                    td2 = tr_elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)")
                    td3 = tr_elem.find_element(By.CSS_SELECTOR, "td:nth-child(3)")
                    if(td2.text.strip() != ""):
                        report_name = td2.text.strip()
                        check_id = td1.find_element(By.CSS_SELECTOR, "input").get_attribute("id")
                        date_id = td3.find_element(By.CSS_SELECTOR, "input").get_attribute("id")
                        ret[report_name] = (check_id, date_id)
                    td1 = tr_elem.find_element(By.CSS_SELECTOR, "td:nth-child(4)")
                    td2 = tr_elem.find_element(By.CSS_SELECTOR, "td:nth-child(5)")
                    td3 = tr_elem.find_element(By.CSS_SELECTOR, "td:nth-child(6)")
                    if(td2.text.strip() != ""):
                        report_name = td2.text.strip()
                        check_id = td1.find_element(By.CSS_SELECTOR, "input").get_attribute("id")
                        date_id = td3.find_element(By.CSS_SELECTOR, "input").get_attribute("id")
                        ret[report_name] = (check_id, date_id)
                break
        return ret

    def entry_teishutsu_shorui(self, shorui_name="", is_check=True, date_ymd=""):
        ctrl_dict = self.get_teishutsu_shorui_ctrl_info()
        if(shorui_name != ""):
            ctrl_info = ctrl_dict.get(shorui_name,None)
            if(ctrl_info is not None):
                if(is_check):
                    self.form_input_by_id(ctrl_info[0],value="1")
                else:
                    self.form_input_by_id(ctrl_info[0],value="0")
                if(date_ymd != ""):
                    self.form_input_by_id(ctrl_info[1],value=date_ymd)
        else:
            for k in ctrl_dict.keys():
                ctrl_info = ctrl_dict.get(k, None)
                if(ctrl_info is not None):
                    if(is_check):
                        self.form_input_by_id(ctrl_info[0],value="1")
                    else:
                        self.form_input_by_id(ctrl_info[0],value="0")
                    if(date_ymd != ""):
                        self.form_input_by_id(ctrl_info[1],value=date_ymd)


    def click_new_tab_button(self):
        old_window_list = self.driver.window_handles[:]
        elm = self.click_by_id("wrNewTab")
        # 現在表示したタブのハンドルに切り替え
        wait = WebDriverWait(self.driver, 10)
        wait.until(EC.number_of_windows_to_be(len(old_window_list)+1))
        for window_handle in reversed(self.driver.window_handles):
            if window_handle not in old_window_list:
                self.driver.switch_to.window(window_handle)
                break
    
    def close_other_tab(self):
        current_handle = self.driver.current_window_handle
        for item in [v for v in self.driver.window_handles if v != current_handle]:
            self.driver.switch_to.window(item)
            self.driver.close()
            self.driver.switch_to.window(current_handle)
    
    def do_login_kodomo(self, user_id=None, password=None):
        """ログイン→メインメニューの遷移"""
        if user_id is None or password is None:
            user_id = self.settings_dict.get("user_id","")
            password = self.settings_dict.get("password","")
        
        login_url = urljoin(self.settings_dict.get("base_url",""), self.settings_dict.get("login_url",""))
        self.get(login_url)
        self.wait_page_loaded()
        self.send_keys_by_name(self.settings_dict.get("user_id_element",""), user_id)
        self.send_keys_by_name(self.settings_dict.get("password_element",""), password)
        self.click_by_name(self.settings_dict.get("login_element",""))
        self.click_kodomo_in_base_menu()

    def click_fukushi_in_base_menu(self):
        self.click_by_id(self.settings_dict.get("fukushi_btn_element",""))
    def click_kodomo_in_base_menu(self):
        self.click_base_menu_and_change_tab("kodomo_btn_element")
    def click_bango_in_base_menu(self):
        self.click_base_menu_and_change_tab("bango_btn_element")
    def click_seiho_in_base_menu(self):
        self.click_base_menu_and_change_tab("seiho_btn_element")
    def click_chugoku_in_base_menu(self):
        self.click_base_menu_and_change_tab("chugoku_btn_element")
    def click_euc_std_in_base_menu(self):
        self.click_base_menu("euc_std_btn_element")
    def click_euc_nostd_in_base_menu(self):
        self.click_base_menu("euc_nostd_btn_element")
    def click_euc_std_real_in_base_menu(self):
        self.click_base_menu("euc_std_real_btn_element")
    def click_euc_nostd_real_in_base_menu(self):
        self.click_base_menu("euc_nostd_real_btn_element")
    def click_online_manual_in_base_menu(self):
        self.click_base_menu_by_label("オンラインマニュアル")
    
    def click_base_menu(self, key_str):
        self.click_by_id(self.settings_dict.get(key_str,""))
    def click_base_menu_by_label(self, label_value):
        self.click_input_button_by_label(label_value)
    def click_base_menu_and_change_tab(self, key_str):
        old_window_list = self.driver.window_handles[:]
        self.click_by_id(self.settings_dict.get(key_str,""))
        # 現在表示したタブのハンドルに切り替え
        wait = WebDriverWait(self.driver, 10)
        wait.until(EC.number_of_windows_to_be(len(old_window_list)+1))
        for window_handle in reversed(self.driver.window_handles):
            if window_handle not in old_window_list:
                self.driver.switch_to.window(window_handle)
                break
    
    def transit_kodomo(self):
        self.click_new_tab_button()
        self.click_kodomo_in_base_menu()
    def transit_bango(self):
        self.click_new_tab_button()
        self.click_bango_in_base_menu()
    def transit_seiho(self):
        self.click_new_tab_button()
        self.click_seiho_in_base_menu()
    def transit_chugoku(self):
        self.click_new_tab_button()
        self.click_chugoku_in_base_menu()
    def transit_euc_std(self):
        self.click_new_tab_button()
        self.click_euc_std_in_base_menu()
    def transit_euc_nostd(self):
        self.click_new_tab_button()
        self.click_euc_nostd_in_base_menu()
    def transit_euc_std_real(self):
        self.click_new_tab_button()
        self.click_euc_std_real_in_base_menu()
    def transit_euc_nostd_real(self):
        self.click_new_tab_button()
        self.click_euc_nostd_real_in_base_menu()
    def transit_online_manual(self):
        self.click_new_tab_button()
        self.click_online_manual_in_base_menu()
        
    def get_euc_table(self, table_name, is_dl_csv=False):
        """EUCテーブル参照を行う"""
        WebDriverHandleHelper.change_euc_driver(self.driver)
        WebDriverHandleHelper.wait_euc_loading_visible(self.driver)

        WebDriverHandleHelper.open_euc_by_name(self.driver, table_name)
        self.screen_shot(table_name)

        WebDriverHandleHelper.euc_search_click(self.driver)
        self.screen_shot(f"{table_name}_search")

        # is_dl_csv=Trueの場合: CSV出力ボタン押下
        # CSV出力ボタン セレクタ: "button.square_btn_front getCsv"
        # ファイル名: "yyyymmdd[結果]{table_name}"
        if is_dl_csv:
            current_date = datetime.datetime.now().strftime("%Y%m%d")
            self.find_element_by_css_selector("button.square_btn_front.getCsv").click()
            self.form_input_by_id("csvFileName", value=f"{current_date}[結果]{table_name}")
            self.form_input_by_id("encodingType", value="utf-8")
            self.click_button_by_label("出力")
            self.screen_shot(f"{table_name}_csv")
            self.assert_message_confirm_popup("CSV出力に成功しました。\nCSV出力リストから確認できます。")
            self.click_button_by_label("はい")

        actions = ActionChains(self.driver)
        # f"li[title=\"[結果]\{table_name}"] > img.tabCloseIcon"
        for v in self.find_elements_by_css_selector(f"li[title=\"[結果]\{table_name}\"]"):
            actions.move_to_element(v).perform()
            v.find_element(By.CSS_SELECTOR, "img.tabCloseIcon").click()
            break

        self.screen_shot(f"{table_name}_close")
        for v in self.find_elements_by_css_selector(f"li[title={table_name}]"):
            actions.move_to_element(v).perform()
            v.find_element(By.CSS_SELECTOR, "img.tabCloseIcon").click()
            break
    
    def jukyu_joukyou_crawl(self, atena_code):
        common_button_ids = []
        gyoumu_button_ids = []

        self.goto_jukyu_joukyou(atena_code)
        self.screen_shot("受給状況")
        
        self.open_common_buttons_area()
        
        for btn in self.find_common_buttons():
            id_str = btn.get_attribute("id")
            common_button_ids.append((id_str, str(btn.text).strip()))
        
        for btn in self.find_jukyu_joukyou_gyoumu_buttons():
            id_str = btn.get_attribute("id")
            tmp = id_str.split(":")
            gcode = tmp[2] if len(tmp) > 2 else "NONE"
            gyoumu_button_ids.append((id_str, str(btn.text).strip(), gcode))
        
        for btn_info in common_button_ids:
            self.__crawl_transit_common_button_page(btn_info, atena_code)
            
        for btn_info in gyoumu_button_ids:
            self.__crawl_transit_gyoumu_button_page(btn_info, atena_code)
    
    def __crawl_transit_common_button_page(self, btn_info, atena_code):
        # btn_info: (id, text)
        try:
            self.click_by_id(btn_info[0])
            self.screen_shot(f"{btn_info[0]}_{btn_info[1]}")
            self.return_click()
            self.open_common_buttons_area()
        except:
            self.goto_jukyu_joukyou(atena_code)
            self.open_common_buttons_area()

    def __crawl_transit_gyoumu_button_page(self, btn_info, atena_code):
        # btn_info: (id, text, gyoumu_code)
        try:
            self.click_by_id(btn_info[0])
            self.screen_shot(f"{btn_info[2]}_{btn_info[1]}")
            if(self.click_rireki_select()):  # 履歴選択画面の場合は資格履歴まで遷移する
                self.screen_shot(f"{btn_info[2]}_{btn_info[1]}_rireki")
                self.return_click()    
            self.return_click()
        except:
            self.goto_jukyu_joukyou(atena_code)

    def get_online_reports_info(self):
        """資格管理の帳票発行画面に表示されている帳票欄の情報を取得する"""
        ret = []
        report_tr_list = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
        for v in report_tr_list:
            report_name = v.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text
            rec_no = ""
            for x in v.find_elements(By.CSS_SELECTOR, "td input"):
                # print(x.get_attribute("id"))
                id_value = x.get_attribute("id")
                if("insatsu" in id_value):
                    tmp_str = str(id_value).split('_')
                    if(len(tmp_str) > 1):
                        rec_no = str(tmp_str[1]).strip()
                        break
            if(rec_no != ""):
                ret.append(OnlineReportsControleInfo(report_name, rec_no))
        return ret

    def is_new_online_parameter(self):
        """オンライン帳票発行画面が新レイアウトのものか判定する"""
        ret = False
        param_header_th = self.find_elements_by_css_selector("#_wrFk_TitleTable_1 > tbody > tr > th")
        for v in param_header_th:
            # print(v.text)
            if(v.text.strip() == "パラメータ情報"):
                ret = True
                break
        return ret

    def get_online_reports_info_new_layout(self):
        ret = []
        report_tr_list = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
        for v in report_tr_list:
            report_name = v.find_element(By.CSS_SELECTOR, "td:nth-child(2)").text
            print(report_name)

            # param_header_th = v.find_element(By.CSS_SELECTOR, "div[id^='parameter_'] > #_wrFk_TitleTable_Div_1 > table > tbody > tr > th > label")
            param_header_th = v.find_elements(By.CSS_SELECTOR, "#_wrFk_TitleTable_Div_1 > table > tbody > tr > th > label")
            for v in param_header_th:
                print(v.text,v.get_attribute("id"))
                p_idx = v.get_attribute("id").replace("title", "")
                print(p_idx)
            
    def print_online_reports(self, case_name="", report_name="", hakkou_ymd="", kigo="", bango="", eda="", report_name_list=None, report_param_list=None):
        """資格管理の帳票発行画面で帳票名指定で各種指定された入力を行い帳票出力を行う"""
        checked_reports_count = 0
        if(self.is_new_online_parameter()):
            checked_reports_count = self.print_online_reports_new(case_name=case_name, report_param_list=report_param_list)
        else:
            checked_reports_count = self.print_online_reports_old(case_name=case_name, report_name=report_name, hakkou_ymd=hakkou_ymd, kigo=kigo, bango=bango, eda=eda, report_name_list=report_name_list)
        return checked_reports_count

    def print_online_reports_old(self, case_name="", report_name="", hakkou_ymd="", kigo="", bango="", eda="", report_name_list=None):
        reports_list = self.get_online_reports_info()
        checked_reports_count = 0
        name_list = []
        if(report_name_list is not None and type(report_name_list) == list):
            for v in report_name_list:
                name_list.append(v)
        if(report_name != ""):
            name_list.append(report_name)

        for item in reports_list:
            is_checked = False
            if(len(name_list) > 0 and item.report_name in name_list):
                item.check_insatsu(self.driver)
                checked_reports_count += 1
                is_checked=True
            elif(len(name_list) == 0):
                item.check_insatsu(self.driver)
                checked_reports_count += 1
                is_checked=True
            if(is_checked):
                if(hakkou_ymd != ""):
                    item.input_hakko_ymd(self.driver,hakkou_ymd)
                if(kigo != ""):
                    item.input_bunsho_kigo(self.driver,kigo)
                if(bango != ""):
                    item.input_bunsho_bango(self.driver,bango,eda)
        if(checked_reports_count > 0):
            self.exec_online_print(case_name=case_name)
        return checked_reports_count
    
    def print_online_reports_new(self,case_name="", report_param_list=None):
        reports_list = self.get_online_reports_info()
        checked_reports_count = 0
        for v in reports_list:
            for x in [p for p in report_param_list if p["report_name"] == v.report_name]:
                v.check_insatsu(self.driver)
                if x.get("params") is not None and len(x["params"]) > 0:
                    v.setup_params(self.driver)
                    v.set_params(self.driver, x["params"])
                checked_reports_count += 1
        if(checked_reports_count > 0):
            self.exec_online_print(case_name=case_name)
        return checked_reports_count
    
    def check_online_report_exist(self, report_name):
        has_report = False
        try:
            reports_list = self.get_online_reports_info()
            for v in reports_list:
                if report_name == v.report_name:
                    has_report = True
                    break
        except:
            pass
        return has_report
    
    def switch_online_report_type(self, report_type=""):
        """帳票印刷画面で、「申請書に切替」リンク押下処理　帳票タイプ：申請書、その他帳票"""
        switch_report_type = f'{report_type}に切替'
        try:
            for elm in self.find_elements_by_css_selector('a[href^="javascript:QAZF024KirikaeClick"]'):
                if report_type != "" and switch_report_type == elm.text.strip():
                    elm.click()
                    break
        except:
            return

    def check_batch_job_exist(self, job_label):
        has_job = False
        for v in self.find_elements_by_css_selector("td.wr_Css_table_LB"):
            if job_label == v.text.strip():
                has_job = True
                break
        return has_job
    
    def get_post_code(self):
        # 医療機関郵便番号上３桁「123」を入力医療機関郵便番号下４桁「4567」を入力
        # CmdShinai押下→Kana1に「ｱ」入力→KensakuKana押下→Sel23チェック→Sel232チェック→Sel0003チェック→Sel104チェック→Banchi「100」入力→Commit押下
        # self.click_by_id("CmdShinai")
        # self.form_input_by_id(idstr="Yubin1", value="123")
        # self.form_input_by_id(idstr="Yubin2", value="4567")
        # self.click_by_id(idstr="KensakuYubinBango")
        # self.form_input_by_id(idstr="Kana1", value="ｱ")
        # self.click_by_id(idstr="KensakuKana")
        # self.form_input_by_id(idstr="Sel23", value="1")
        # time.sleep(1)
        # self.form_input_by_id(idstr="Sel232", value="1")
        # time.sleep(1)
        # self.form_input_by_id(idstr="Sel0003", value="1")
        # time.sleep(1)
        # self.form_input_by_id(idstr="Sel104", value="1")
        # time.sleep(1)
        # self.form_input_by_id(idstr="Banchi", value="100")
        # time.sleep(1)
        # self.click_by_id("Check")
        # time.sleep(1)
        # self.click_button_by_label("入力決定(Enter)")
        self.click_by_id("CmdShinai")
        self.form_input_by_id(idstr="Yubin1", value="123")
        self.form_input_by_id(idstr="Yubin2", value="4567")
        self.click_by_id(idstr="KensakuYubinBango")
        self.form_input_by_id(idstr="Kana1", value="ｱ")
        self.click_by_id(idstr="KensakuKana")
        self.form_input_by_id(idstr="Sel23", value="1")
        time.sleep(1)
        self.form_input_by_id(idstr="Sel232", value="1")
        time.sleep(1)
        self.form_input_by_id(idstr="Sel0003", value="1")
        time.sleep(1)
        self.form_input_by_id(idstr="Sel104", value="1")
        time.sleep(1)
        self.form_input_by_id(idstr="Banchi", value="100")
        time.sleep(1)
        self.click_by_id("Check")
        time.sleep(1)
        self.click_button_by_label("入力決定(Enter)")

    def click_oldest_hennou(self):
        """返納状況一覧リスト内最古(一番下)のNoボタンを押下する"""
        # 以下セレクタに該当する要素の中で一番最後のボタンをクリックする
        # セレクタ: "#_wrFk_TitleTable_Div_1 > .wr_table > td > button"
        last_button = ''
        list_tbody_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
        for elem in list_tbody_elem:
            for x in elem.find_elements(By.CSS_SELECTOR, "td button"):
                last_button = x.text.strip()

        if last_button != '':
            self.click_button_by_label(last_button)

    def exist_shintatsu_info(self):
        """進捗情報が存在しているかどうか判定する"""
        return self.exist_item(item_type="input", item_id="TxtShintatsu1YMD")

    def exist_hanntei_info(self):
        """判定情報が存在しているかどうか判定する"""
        return self.exist_item(item_type="button", item_name="判定情報")
    
    def exist_item(self, item_type, item_id="", item_name=""):
        """項目が存在しているかどうか判定する"""
        is_exist = False
        for v in self.find_elements_by_css_selector(item_type):
            if (item_id != "" and item_id == v.get_attribute("id")) or (item_name != "" and item_name == v.text.strip()):
                is_exist = True
                break
        return is_exist

    def convert_to_japanese_era(self,date: str) -> str:
        if not date.isdigit() or len(date) != 6:
            raise ValueError(f"Invalid input format: {date}")

        yyyymm = int(date)
        year = yyyymm // 100
        month = yyyymm % 100

        if year >= 2019:
            era = "R"
            era_year = year - 2018
        elif year >= 1989:
            era = "H"
            era_year = year - 1988
        elif year >= 1926:
            era = "S"
            era_year = year - 1925
        else:
            raise NoSuchElementException(msg=f"無効な年です: {year} ")

        return f"{era}{era_year:02}.{month:02}"

class OnlineReportsControleInfo:
    """資格管理のオン帳票情報保持クラス"""
    def __init__(self, report_name, rec_no):
        self.report_name = str(report_name).strip()
        self.rec_no = rec_no

        self.insatsuChkId = f"insatsuChk_{rec_no}"
        self.hakkoChkId = f"hakkoChk_{rec_no}"
        self.HakkoTxtId = f"HakkoTxt_{rec_no}"
        self.kigoChkId = f"kigoChk_{rec_no}"
        self.kigoTxtId = f"kigoTxt_{rec_no}"
        self.bangoChkId = f"bangoChk_{rec_no}"
        self.bangoTxtId = f"bangoTxt_{rec_no}"
        self.edaTxtId = f"edaTxt_{rec_no}"
        self.koinChkId = f"koinChk_{rec_no}"
        self.param_list = []
        self.param_div_id = f"parameter2_{rec_no}"
        self.param_input_div_id = f"parameter_{rec_no}"
        self.param_complete_button_id = f"parameterChk_{rec_no}"

    def check_insatsu(self, driver):
        if(WebDriverHandleHelper.has_elements_by_css_selector(driver, f"#{self.insatsuChkId}")):
            elm = WebDriverHandleHelper.find_element_by_id(driver, self.insatsuChkId)
            if(not elm.is_selected()):
                elm.click()
        
    def input_hakko_ymd(self, driver, input_ymd):
        elem = WebDriverHandleHelper.find_element_by_id(driver, self.hakkoChkId)
        if(elem.is_displayed()):
            WebDriverHandleHelper.click_by_id(driver, self.hakkoChkId)
            WebDriverHandleHelper.send_keys_by_id(driver, self.HakkoTxtId, value=input_ymd)

    def input_bunsho_kigo(self, driver, kigo):
        elem = WebDriverHandleHelper.find_element_by_id(driver, self.kigoTxtId)
        if(elem.is_displayed()):
            WebDriverHandleHelper.click_by_id(driver, self.kigoChkId)
            WebDriverHandleHelper.send_keys_by_id(driver, self.kigoTxtId, value=kigo)
    
    def input_bunsho_bango(self, driver, bango, eda=""):
        elem = WebDriverHandleHelper.find_element_by_id(driver, self.bangoTxtId)
        if(elem.is_displayed()):
            WebDriverHandleHelper.click_by_id(driver, self.bangoChkId)
            WebDriverHandleHelper.send_keys_by_id(driver, self.bangoTxtId, value=bango)
            if(eda!=""):
                elem2 = WebDriverHandleHelper.find_element_by_id(driver, self.edaTxtId)
                if(elem2.is_displayed()):
                    WebDriverHandleHelper.send_keys_by_id(driver, self.edaTxtId, value=eda)
    
    def set_params(self, driver , params):
        for p in params:
            self.set_param_by_title(driver, title=p.get("title", ""), value=p.get("value", ""))
            if(p.get("is_no_print", None) is not None):
                self.set_hakkou_ymd_is_no_print(driver, title=p.get("title", ""), value=p.get("is_no_print", "0"))
        self.params_input_complete(driver)

    def set_param_by_title(self, driver, title="", value=""):
        tmp = [v for v in self.param_list if v.param_title == title]
        if(len(tmp) > 0):
            item = tmp[0]
            item.input_value(driver, value=value)

    def set_hakkou_ymd_is_no_print(self, driver, title="発行年月日", value="0"):
        tmp = [v for v in self.param_list if v.param_title == title]
        if(len(tmp) > 0):
            item = tmp[0]
            if(item.param_has_no_print_check):
                item.no_print_check(driver, value=value)

    def setup_params(self, driver):
        self.params_input_mode(driver)
        self.param_list = self.get_params_info(driver)
    
    def dummy_params_input(self, driver):
        for item in self.param_list:
            item.print_self()
            if(item.param_is_required):
                item.input_dummy_value(driver)
        self.params_input_complete(driver)
    
    def get_params_info(self, driver):
        ret = []
        if(WebDriverHandleHelper.has_elements_by_css_selector(driver, f"#{self.param_input_div_id}")):
            
            param_header_tr = WebDriverHandleHelper.find_elements_by_css_selector(driver, f"div[id^='{self.param_input_div_id}'] > #_wrFk_TitleTable_Div_1 > table > tbody > tr")
            for param_rec in param_header_tr:
                p_item = OnlineReportsParameterItem()
                title_label_elem = param_rec.find_element(By.CSS_SELECTOR, "th > label")
                p_item.param_title = title_label_elem.text.strip()
                p_item.param_idx = title_label_elem.get_attribute("id").replace("title", "")
                if(p_item.param_title == "発行年月日"):
                    p_item.param_is_hakkou_ymd = True

                for tmpElem in param_rec.find_elements(By.CSS_SELECTOR, "td > input"):
                    type_name = tmpElem.get_attribute("type").strip()
                    if(type_name != "hidden"):
                        p_item.param_input_id = tmpElem.get_attribute("id").strip()
                        p_item.param_input_type = str(type_name).lower()
                        break
                if(p_item.param_input_id == ""):
                    for tmpElem in param_rec.find_elements(By.CSS_SELECTOR, "td > select"):
                        p_item.param_input_id = tmpElem.get_attribute("id").strip()
                        p_item.param_input_type = "select"
                        break
                
                for tmpElem in param_rec.find_elements(By.CSS_SELECTOR, "td"):
                    p_item.param_input_text = str(tmpElem.text).replace("\"","").strip()
                    break
                if(p_item.param_title == ""):
                    if(p_item.param_input_text != ""):
                        p_item.param_title = p_item.param_input_text
                if("帳票印字なし" in p_item.param_input_text):
                    for tmpElem in param_rec.find_elements(By.CSS_SELECTOR, "td > input"):
                        type_name = tmpElem.get_attribute("type").strip().lower()
                        if(type_name == "checkbox"):
                            p_item.param_has_no_print_check = True
                            p_item.param_no_print_check_id = tmpElem.get_attribute("id").strip()
                            break

                for tmpElem in param_rec.find_elements(By.CSS_SELECTOR, "img"):
                    src_path = tmpElem.get_attribute("src")
                    if("wrFk_Icon_optional" in src_path):
                        pass
                    if("wrFk_Icon_required" in src_path):
                        p_item.param_is_required = True
                    if("calendar.gif" in src_path):
                        onclick_src = tmpElem.get_attribute("onclick")
                        if("type=ymd" in onclick_src):
                            p_item.param_is_ymd_input = True
                        elif("type=ym" in onclick_src):
                            p_item.param_is_ym_input = True
                ret.append(p_item)
        return ret

    def params_input_mode(self, driver):
        if(WebDriverHandleHelper.has_elements_by_css_selector(driver, f"#{self.param_div_id}")):
            param_header_th = WebDriverHandleHelper.find_elements_by_css_selector(driver, f"div[id^='{self.param_div_id}'] > table > tbody > tr > th")
            if(len(param_header_th) > 0):
                param_header_th[0].click()

    def params_input_complete(self, driver):
        if(WebDriverHandleHelper.has_elements_by_css_selector(driver, f"#{self.param_complete_button_id}")):
            WebDriverHandleHelper.click_by_id(driver, self.param_complete_button_id)


class OnlineReportsParameterItem:
    def __init__(self):
        self.param_title = ""
        self.param_idx = ""
        self.param_input_id = ""
        self.param_input_type = ""
        self.param_input_text = ""
        self.param_is_hakkou_ymd = False
        self.param_has_no_print_check = False
        self.param_no_print_check_id = ""
        self.param_is_required = False
        self.param_is_ymd_input = False
        self.param_is_ym_input = False
    
    def input_value(self, driver, value=""):
        if(self.param_input_type != "select"):
            if(self.param_input_type in ["radio", "checkbox"]):
                self.input_check(driver, value=value)
            else:
                WebDriverHandleHelper.send_keys_by_id(driver, self.param_input_id, value=value)
        else:
            WebDriverHandleHelper.select_by_id(driver, self.param_input_id, text=value)
    
    def input_check(self, driver, value=""):
        elem = WebDriverHandleHelper.find_element_by_id(driver, self.param_input_id)
        if(value=="1"):
            if(not elem.is_selected()):
                elem.click()
        elif(value=="0"):
            if(elem.is_selected()):
                elem.click()
    def no_print_check(self, driver, value=""):
        if(self.param_has_no_print_check):
            elem = WebDriverHandleHelper.find_element_by_id(driver, self.param_no_print_check_id)
            if(value=="1"):
                if(not elem.is_selected()):
                    elem.click()
            elif(value=="0"):
                if(elem.is_selected()):
                    elem.click()

    def input_dummy_value(self, driver):
        if(self.param_input_type != "select"):
            if(self.param_is_ymd_input):
                WebDriverHandleHelper.send_keys_by_id(driver, self.param_input_id, value=datetime.datetime.now().strftime("%Y%m%d"))
            elif(self.param_is_ym_input):
                WebDriverHandleHelper.send_keys_by_id(driver, self.param_input_id, value=datetime.datetime.now().strftime("%Y%m"))
            else:
                WebDriverHandleHelper.send_keys_by_id(driver, self.param_input_id, value="1")
        else:
            # selectは何か基本的に選ばれているので不要
            pass
    
    def print_self(self):
        print(self.str_self())

    def str_self(self):
        return f"{self.param_title} - {self.param_input_id} - {self.param_input_type} - {self.param_is_hakkou_ymd} - {self.param_has_no_print_check} - {self.param_no_print_check_id} - {self.param_is_required} - {self.param_is_ymd_input} - {self.param_is_ym_input}"
