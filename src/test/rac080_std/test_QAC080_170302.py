import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC080_170302(FukushiSiteTestCaseBase):
    """TestQAC080_170302"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 住記異動による転出・死亡等の受給者・対象児童の抽出を行い、消滅対象者の抽出が行えることを確認する。
    def test_QAC080_170302(self):
        """住記上で除票となった対象者の抽出"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 4 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_4")

        # 5 バッチ起動画面: 業務：児童事業：児童手当処理区分：月次処理分類：住記異動者自動消滅
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="住記異動者自動消滅")

        # 6 バッチ起動画面: 「住記異動者自動消滅_抽出」のNoボタン押下
        self.click_batch_job_button_by_label("住記異動者自動消滅_抽出")

        # 7 バッチ起動画面: 対象開始年月日：20231001対象終了年月日：20231031出力順：認定番号順
        params = [
            {"title": "対象開始年月日", "type": "text", "value": case_data.get("taishou_kaishi_YMD", "")},
            {"title": "対象終了年月日", "type": "text", "value": case_data.get("taishou_shuuryou_YMD", "")},
            {"title": "出力順", "type": "select", "value": case_data.get("shutsuryokujun", "")}
        ]
        self.set_job_params(params)

        # 8 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 11 ジョブ実行履歴画面: 表示
        # Assert: 「住記異動者自動消滅_抽出」　の　処理結果「正常終了」を確認
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「No1」ボタン押下
        # self.click_button_by_label("1")

        # 13 ジョブ実行詳細画面: 表示
        self.screen_shot("ジョブ実行詳細画面_13")

        # 14 ジョブ実行詳細画面: 「ログ」ボタン押下
        self.click_button_by_label("ログ")

        # 15 ジョブ実行ログ画面: 表示
        self.screen_shot("ジョブ実行ログ画面_15")

        # 16 ジョブ実行ログ画面: 「戻る」ボタン押下
        self.return_click()

        # 17 ジョブ実行詳細画面: 表示
        self.screen_shot("ジョブ実行詳細画面_17")

        # 18 ジョブ実行詳細画面: 「戻る」ボタン押下
        self.return_click()

        # 19 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_19")

        # 20 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 21 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_21")

        # 22 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 23 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_23")

        # 24 ジョブ帳票履歴画面: 「自動消滅 資格喪失対象者一覧」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 資格喪失対象者一覧")

        # 25 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 26 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_26")

        # 27 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 28 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_28")

        # 29 ジョブ帳票履歴画面: 「自動消滅 資格喪失対象者一覧_差止中」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 資格喪失対象者一覧_差止中")

        # 30 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 31 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_31")

        # 32 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 33 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_33")

        # 34 ジョブ帳票履歴画面: 「自動消滅 資格喪失対象者一覧_現況未提出」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 資格喪失対象者一覧_現況未提出")

        # 35 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 36 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_36")

        # 37 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 38 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_38")

        # 39 ジョブ帳票履歴画面: 「自動消滅 資格喪失対象者一覧_最新履歴未決定」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 資格喪失対象者一覧_最新履歴未決定")

        # 40 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 41 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_41")

        # 42 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 43 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_43")

        # 44 ジョブ帳票履歴画面: 「自動消滅 資格喪失対象者一覧_履歴逆転分」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 資格喪失対象者一覧_履歴逆転分")

        # 45 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 46 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_46")

        # 47 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 48 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_48")

        # 49 ジョブ帳票履歴画面: 「自動消滅 資格喪失対象者一覧_所得なし」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 資格喪失対象者一覧_所得なし")

        # 50 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 51 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_51")

        # 52 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 53 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_53")

        # 54 ジョブ帳票履歴画面: 「自動消滅 資格喪失対象者一覧_児童転出」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 資格喪失対象者一覧_児童転出")

        # 55 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 56 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_56")

        # 57 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 58 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_58")

        # 59 ジョブ帳票履歴画面: 「自動消滅 資格喪失対象者一覧_年齢到達」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 資格喪失対象者一覧_年齢到達")

        # 60 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 61 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_61")

        # 62 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 63 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_63")

        # 64 ジョブ帳票履歴画面: 「自動消滅 額改定対象者一覧」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 額改定対象者一覧")

        # 65 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 66 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_66")

        # 67 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 68 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_68")

        # 69 ジョブ帳票履歴画面: 「自動消滅 額改定対象者一覧_差止中」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 額改定対象者一覧_差止中")

        # 70 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 71 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_71")

        # 72 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 73 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_73")

        # 74 ジョブ帳票履歴画面: 「自動消滅 額改定対象者一覧_現況未提出」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 額改定対象者一覧_現況未提出")

        # 75 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 76 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_76")

        # 77 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 78 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_78")

        # 79 ジョブ帳票履歴画面: 「自動消滅 額改定対象者一覧_最新履歴未決定」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 額改定対象者一覧_最新履歴未決定")

        # 80 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 81 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_81")

        # 82 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 83 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_83")

        # 84 ジョブ帳票履歴画面: 「自動消滅 額改定対象者一覧_履歴逆転分」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 額改定対象者一覧_履歴逆転分")

        # 85 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 86 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_86")

        # 87 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 88 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_88")

        # 89 ジョブ帳票履歴画面: 「自動消滅 額改定対象者一覧_所得なし」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 額改定対象者一覧_所得なし")

        # 90 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 91 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_91")

        # 92 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 93 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_93")

        # 94 ジョブ帳票履歴画面: 「自動消滅 額改定対象者一覧_児童転出」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 額改定対象者一覧_児童転出")

        # 95 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 96 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_96")

        # 97 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 98 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_98")

        # 99 ジョブ帳票履歴画面: 「自動消滅 額改定対象者一覧_年齢到達」　の　「No」ボタン押下
        # self.click_batch_job_button_by_label("自動消滅 額改定対象者一覧_年齢到達")

        # 100 ジョブ帳票履歴画面: 「ファイルを開く(O)」ボタンを押下

        # 101 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_101")

        # 102 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 103 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_103")
