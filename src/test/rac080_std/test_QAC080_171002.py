import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC080_171002(FukushiSiteTestCaseBase):
    """TestQAC080_171002"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # ・所得一括照会、年金一括照会、住基一括照会の抽出、結果の画面確認・帳票確認ができることを確認する。
    def test_QAC080_171002(self):
        """照会対象者および結果の確認登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー画面: 「所得一括照会」ボタン押下
        self.click_button_by_label("所得一括照会")

        # 4 所得一括照会対象者検索画面: 表示
        self.screen_shot("所得一括照会対象者検索画面_4")

        # 5 所得一括照会対象者検索画面: 業務：児童事業：児童手当処理区分：照会結果の確認　チェック
        self.form_input_by_id(idstr="Cmb業務", text="児童")
        self.form_input_by_id(idstr="Cmb事業", text="児童手当")
        self.form_input_by_id(idstr="Rdo処理区分2", value="1")

        # 6 所得一括照会対象者検索画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 7 所得一括照会対象者検索画面: 表示
        self.screen_shot("所得一括照会対象者検索画面_7")

        # 8 所得一括照会対象者検索画面: 対象年度：令和5年事務コード：７４＿児童手当法による児童手当又は特例給付の支給に関する事務であって主務省令で定めるもの事務手続コード：５６‐２５＿認定の請求に係る事実の審査（世帯構成員の確認）　　　　選択
        self.form_input_by_id(idstr="Cmb事務", text=case_data.get("jimuCd", ""))
        self.form_input_by_id(idstr="Cmb事務手続", text=case_data.get("jimuTetsudzukiCd", ""))
        self.form_input_by_id(idstr="Cmb対象年度", text=case_data.get("taisho_nendo", ""))

        # 9 所得一括照会対象者検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 10 所得一括照会対象者選択画面: 表示
        self.screen_shot("所得一括照会対象者選択画面_10")

        # 11 所得一括照会対象者選択画面: 「戻る」ボタン押下
        self.return_click()

        # 12 所得一括照会対象者検索画面: 表示
        self.screen_shot("所得一括照会対象者検索画面_12")

        # 14 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_14")

        # 15 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 16 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_16")

        # 17 バッチ起動画面: 業務：児童事業：児童手当処理区分：住基照会抽出処理分類：住基照会抽出
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="住基照会抽出")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="住基照会抽出")
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「住基照会結果確認書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("住基照会結果確認書出力処理")

        # 19 バッチ起動画面: 出力順序：受給者名称（昇順）　　選択
        params = [
            {"title": "出力順序", "type": "select", "value": case_data.get("shutsuryoku_junjo", "")}
        ]
        self.set_job_params(params)

        # 20 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_20")

        # 21 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 22 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_22")

        # 23 ジョブ実行履歴画面: 「検索」ボタン押下
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 24 ジョブ実行履歴画面: 表示
        # Assert: 状態：正常終了　をチェック
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 26 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_26")

        # 27 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 28 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_28")

        # 29 ジョブ帳票履歴画面: 「住基照会結果確認書」のNoボタン押下
        # self.click_batch_job_button_by_label("住基照会結果確認書")

        # 30 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 31 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_31")

        # 32 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 33 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_33")
