import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC080_171801(FukushiSiteTestCaseBase):
    """TestQAC080_171801"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # ・47種類のEUCの正常動作を確認する。
    def test_QAC080_171801(self):
        """必要に応じて業務データの抽出・確認"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 EUC画面: 表示
        self.transit_euc_std()
        self.screen_shot("EUC画面_1")

        # 2 EUC画面: テーブル参照「※EUC名」ダブルクリック備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_受給者一覧", is_dl_csv=True)

        # 3 EUC画面: 選択したEUC定義タグ画面の表示
        # self.screen_shot("EUC画面_3")

        # 4 EUC画面: 「検索」ボタン押下
        # self.click_button_by_label("検索")

        # 5 EUC画面: 警告ポップアップ画面の表示
        # Assert: メッセージ「検索結果がありません。」と表示されていることを確認する。
        # self.assert_message_area("検索結果がありません。")
        # self.screen_shot("EUC画面_5")

        # 6 警告ポップアップ画面: 「はい」ボタン押下
        # self.click_button_by_label("はい")

        # 7 EUC画面: [結果]タグ画面の表示
        self.screen_shot("EUC画面_7")

        # 8 EUC画面: 「CSV出力」ボタン押下
        self.click_button_by_label("CSV出力")

        # 9 ファイル出力ポップアップ: ファイル名「(選択したEUC名)」文字コード「utf-8」選択
        self.screen_shot("ファイル出力ポップアップ_9")

        # 10 ファイル出力ポップアップ: 「出力」ボタン押下
        self.click_button_by_label("出力")

        # 11 お知らせポップアップ画面: 表示
        # Assert: メッセージ「CSV出力に成功しました。CSV出力リストから確認できます。」と表示されていることを確認する。
        self.assert_message_area("CSV出力に成功しました。CSV出力リストから確認できます。")
        self.screen_shot("お知らせポップアップ画面_11")

        # 12 お知らせポップアップ画面: 「はい」ボタン押下
        self.click_button_by_label("はい")

        # 13 EUC画面: [結果]タグ画面の表示

        # 14 EUC画面: [結果]タグ画面の×ボタン押下で閉じる

        # 15 EUC画面: 選択したEUC定義タグ画面の表示

        # 16 EUC画面: 選択したEUC定義タグ画面の×ボタン押下で閉じる

        # 17 EUC画面: 表示
        self.screen_shot("EUC画面_17")

        # 18 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_児童一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_18")

        # 19 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_現況一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_19")

        # 20 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_差止一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_20")

        # 21 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_メモ一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_21")

        # 22 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_履歴一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_22")

        # 23 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_同居で住所が違う人リスト", is_dl_csv=True)
        self.screen_shot("EUC画面_23")

        # 24 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_別居で住所が同じ人リスト", is_dl_csv=True)
        self.screen_shot("EUC画面_24")

        # 25 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_不現住者一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_25")

        # 26 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_送付先一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_26")

        # 27 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_送付先一覧_施設", is_dl_csv=True)
        self.screen_shot("EUC画面_27")

        # 28 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_世帯一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_28")

        # 29 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_住民税一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_29")

        # 30 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_税未登録未申告一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_30")

        # 31 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_還付充当一覧", is_dl_csv=True)
        self.screen_shot("EUC画面_31")

        # 32 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_受給者基本情報", is_dl_csv=True)
        self.screen_shot("EUC画面_32")

        # 33 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_住登外者宛名基本情報", is_dl_csv=True)
        self.screen_shot("EUC画面_33")

        # 34 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_対象児童情報", is_dl_csv=True)
        self.screen_shot("EUC画面_34")

        # 35 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_配偶者情報", is_dl_csv=True)
        self.screen_shot("EUC画面_35")

        # 36 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_被用区分情報", is_dl_csv=True)
        self.screen_shot("EUC画面_36")

        # 37 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_住民基本台帳_住民情報", is_dl_csv=True)
        self.screen_shot("EUC画面_37")

        # 38 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_個人住民税_住民税情報", is_dl_csv=True)
        self.screen_shot("EUC画面_38")

        # 39 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_個人住民税_個人納税義務者情報", is_dl_csv=True)
        self.screen_shot("EUC画面_39")

        # 40 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_不現住情報", is_dl_csv=True)
        self.screen_shot("EUC画面_40")

        # 41 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_世帯情報", is_dl_csv=True)
        self.screen_shot("EUC画面_41")

        # 42 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_送付先情報", is_dl_csv=True)
        self.screen_shot("EUC画面_42")

        # 43 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_連絡先情報", is_dl_csv=True)
        self.screen_shot("EUC画面_43")

        # 44 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_特記事項情報", is_dl_csv=True)
        self.screen_shot("EUC画面_44")

        # 45 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_住民基本台帳_支援措置対象者情報", is_dl_csv=True)
        self.screen_shot("EUC画面_45")

        # 46 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_特別事情情報", is_dl_csv=True)
        self.screen_shot("EUC画面_46")

        # 47 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_改定請求情報", is_dl_csv=True)
        self.screen_shot("EUC画面_47")

        # 48 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_現況情報", is_dl_csv=True)
        self.screen_shot("EUC画面_48")

        # 49 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_所得情報", is_dl_csv=True)
        self.screen_shot("EUC画面_49")

        # 50 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_別居監護申立申請情報", is_dl_csv=True)
        self.screen_shot("EUC画面_50")

        # 51 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_支払情報", is_dl_csv=True)
        self.screen_shot("EUC画面_51")

        # 52 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_過誤情報", is_dl_csv=True)
        self.screen_shot("EUC画面_52")

        # 53 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_差止情報", is_dl_csv=True)
        self.screen_shot("EUC画面_53")

        # 54 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_徴収情報", is_dl_csv=True)
        self.screen_shot("EUC画面_54")

        # 55 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_提出書類情報", is_dl_csv=True)
        self.screen_shot("EUC画面_55")

        # 56 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_施設情報", is_dl_csv=True)
        self.screen_shot("EUC画面_56")

        # 57 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_帳票管理情報", is_dl_csv=True)
        self.screen_shot("EUC画面_57")

        # 58 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_金融機関情報", is_dl_csv=True)
        self.screen_shot("EUC画面_58")

        # 59 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_金融機関店舗情報", is_dl_csv=True)
        self.screen_shot("EUC画面_59")

        # 60 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_仕向金融機関情報", is_dl_csv=True)
        self.screen_shot("EUC画面_60")

        # 61 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_振込口座情報", is_dl_csv=True)
        self.screen_shot("EUC画面_61")

        # 62 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_帳票発行履歴情報", is_dl_csv=True)
        self.screen_shot("EUC画面_62")

        # 63 EUC画面: No2～16を備考に記載しているEUC名で実施する。
        self.get_euc_table("児童手当_問い合わせ先情報", is_dl_csv=True)
        self.screen_shot("EUC画面_63")
