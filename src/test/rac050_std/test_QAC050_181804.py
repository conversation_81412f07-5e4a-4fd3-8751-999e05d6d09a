import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181804(FukushiSiteTestCaseBase):
    """TestQAC050_181804"""

    def setUp(self):
        # case_data = self.test_data["TestQAC050_181804"]
        # sql_params = {
        #     "TARGET_GYOUMU_CODE": "QAC050",
        #     "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
        #     "TARGET_NENDO":"2022",
        #     "TARGET_NENDO2":"2023"
        # }
        # self.exec_sqlfile("Test_QAC050_181804.sql", params=sql_params)
        super().setUp()

    # 年齢到達した児童がいる場合、資格喪失の登録ができることを確認する。
    def test_QAC050_181804(self):
        """資格喪失処理"""

        case_data = self.test_data["TestQAC050_181804"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")
        kaitei_ymd = case_data.get("kaitei_ymd", "")

        # =====資格登録=====
        # self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.click_button_by_label("申請内容入力")
        # self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        # self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        # self.click_button_by_label("確定")
        # self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        # self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        # self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        # self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        # self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        # self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("2")
        # self.form_input_by_id(idstr="CmbZokugara", text="子")
        # self.form_input_by_id(idstr="RdoDokyo1", value="1")
        # self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        # self.form_input_by_id(idstr="KojiGaitou2", value="1")
        # self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        # self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        # self.form_input_by_id(idstr="RdoShogai2", value="1")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("3")
        # self.form_input_by_id(idstr="CmbZokugara", text="子")
        # self.form_input_by_id(idstr="RdoDokyo1", value="1")
        # self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        # self.form_input_by_id(idstr="KojiGaitou2", value="1")
        # self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        # self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        # self.form_input_by_id(idstr="RdoShogai2", value="1")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("4")
        # self.form_input_by_id(idstr="CmbZokugara", text="子")
        # self.form_input_by_id(idstr="RdoDokyo1", value="1")
        # self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        # self.form_input_by_id(idstr="KojiGaitou2", value="1")
        # self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        # self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        # self.form_input_by_id(idstr="RdoShogai2", value="1")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("福祉世帯情報")
        # self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        # self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        # self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        # self.form_input_by_id(idstr="HoninCmb_2", text="子")
        # self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        # self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
        # self.form_input_by_id(idstr="HoninCmb_3", text="子")
        # self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        # self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        # self.form_input_by_id(idstr="HoninCmb_4", text="子")
        # self.form_input_by_id(idstr="JukyuCmb_4", text="対象児童")
        # self.form_input_by_id(idstr="GaitoYMDtxt_4", value="20230501")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("口座情報")
        # self.click_button_by_label("追加")
        # self.entry_kouza_info(
        #     start_ymd=kaitei_ymd,
        #     ginko_code="0001",
        #     shiten_code="001",
        #     kouza_shubetsu_text="普通",
        #     kouza_bango="1234567",
        #     koukai=True
        # )
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.return_click()
        # self.form_input_by_id(idstr="TxtKaitei", value="202305")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        # if (can_shintatsu_button):
        #     self.click_button_by_label("本庁進達入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        #     self.click_button_by_label("本庁進達結果入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        #     self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        # self.click_button_by_label("決定内容入力")
        # self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        # self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        # self.form_input_by_id(idstr="TxtShoushoBango", value="181804")
        #
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        #
        # # 支払履歴入力(支払済にする)
        # self.click_button_by_label("修正")
        # self.open_common_buttons_area()
        # self.click_button_by_label("支払履歴")
        # self.click_button_by_label("6")
        # self.click_button_by_label("修正")
        # self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        # self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        # self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        # self.click_button_by_label("最新口座")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        #
        # self.click_button_by_label("5")
        # self.click_button_by_label("修正")
        # self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        # self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        # self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        # self.click_button_by_label("最新口座")
        # self.click_button_by_label("登録")
        # self.alert_ok()

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC050")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        self.wait_page_loaded(wait_timeout=10)

        # 10 児童扶養手当資格管理画面: 申請種別「資格喪失」選択申請理由「２０歳到達」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
        self.screen_shot("児童扶養手当資格管理画面_10")

        # 11 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 児童扶養手当資格管理画面: 請求年月日「20240301」事由発生年月日「20240228」消滅年月日「20240228」職権チェック
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("txt_shinsei_ymd_1", ""))
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=case_data.get("txt_jiyu_hassei_ymd_1", ""))
        self.form_input_by_id(idstr="TxtKaitei", value=case_data.get("txt_kaitei_1", ""))
        self.form_input_by_id(idstr="ShokkenChkBox", value=case_data.get("shokken_chk_box", ""))
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=case_data.get("txt_pre_shikyu_kaishi_ymd", ""))
        self.screen_shot("児童扶養手当資格管理画面_12")

        # 13 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 15 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_15")

        can_shintatsu_button = self.click_button_by_label(case_data.get("shintatsu_button", ""))
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value=case_data.get("txt_shintatsu1_ymd", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")
            self.click_button_by_label(case_data.get("shintatsu_kekka_button", ""))
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value=case_data.get("txt_shintatsu1_hantei_ymd", ""))
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=case_data.get("shintatsu1_hantei_cmb", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")

        # 16 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 17 児童扶養手当資格管理画面: 決定年月日「20240302」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("txt_kettei_ymd", ""))
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=case_data.get("kettei_kekka_cmb", ""))
        self.screen_shot("児童扶養手当資格管理画面_17")

        # 18 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 19 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 20 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_20")
