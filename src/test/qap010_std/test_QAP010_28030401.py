from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28030401(KodomoSiteTestCaseBase):
    """TestQAP010_28030401"""

    def setUp(self):
        super().setUp()

    # 利用者負担額切替対象の在籍児童が確認できることを確認する。
    def test_QAP010_28030401(self):
        """在籍児童確認"""

        case_data_006_ZEAF000400 = self.test_data["006_ZEAF000400"]
        case_data_008_ZEAF000400 = self.test_data["008_ZEAF000400"]
        case_data_009_ZEAF002200 = self.test_data["009_ZEAF002200"]
        tab_index = 0

        # 1, 2 メインメニュー画面:「子ども子育て表示」
        self.do_login_new_tab()

        # 3 メインメニュー画面: メインメニューから「バッチ管理」クリック
        # 4 メインメニュー画面:「即時実行」クリック
        # 5 メインメニュー画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 6 スケジュール個別追加画面: 業務名: <子ども子育て支援>: サブシステム名：<入所>: 処理名：<入所者名簿出力処理>
        # 7 スケジュール個別追加画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_006_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_006_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_006_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加画面_7")

        # 8 スケジュール個別追加画面: 「入所者名簿　出力」の「No」ボタン押下：
        self.click_batch_job_button_by_label(job_label=case_data_008_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 9 実行指示画面: パラメータを入力
        params = [
            {"title": "基準日_開始", "type": "text", "value": case_data_009_ZEAF002200.get("kijun_bi_kaishi", "")},
            {"title": "基準日_終了", "type": "text",
             "value": case_data_009_ZEAF002200.get("kijun_bi_shuuryou", "")},
            {"title": "所管区", "type": "select",
             "value": case_data_009_ZEAF002200.get("shokan_ku", "")},
            {"title": "公私区分", "type": "select",
             "value": case_data_009_ZEAF002200.get("koushi_kubun", "")},
            {"title": "支所", "type": "select",
             "value": case_data_009_ZEAF002200.get("shisho", "")},
            {"title": "施設種類", "type": "select", "value": case_data_009_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "実施区分", "type": "select", "value": case_data_009_ZEAF002200.get("jisshi_kubun", "")},
            {"title": "所在", "type": "select", "value": case_data_009_ZEAF002200.get("shozai", "")},
            {"title": "申込区分", "type": "select", "value": case_data_009_ZEAF002200.get("moushikomi_kubun", "")},
            {"title": "入所形態", "type": "select", "value": case_data_009_ZEAF002200.get("nyusho_keitai", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_009_ZEAF002200.get("hakko_nengetsu_bi", "")},
            {"title": "並び順", "type": "select", "value": case_data_009_ZEAF002200.get("narabi_jun", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_9")

        # 10 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 11 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_11")

        # 12 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 13 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_13")

        # 14 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 15 ファイルダウンロード画面:「No1」ボタン押下
        # 16 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 17 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
