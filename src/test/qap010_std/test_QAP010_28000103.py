from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28000103(KodomoSiteTestCaseBase):
    """TestQAP010_28000103"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28000103(self):
        case_data_036_QAZF100001 = self.test_data["036_QAZF100001"]
        case_data_038_QAZF100002 = self.test_data["038_QAZF100002"]
        case_data_042_QAPF100800 = self.test_data["042_QAPF100800"]
        case_data_048_QAPF100900 = self.test_data["048_QAPF100900"]
        case_data_054_QAPF101200 = self.test_data["054_QAPF101200"]
        case_data_060_QAPF101300 = self.test_data["060_QAPF101300"]
        case_data_066_QAPF101800 = self.test_data["066_QAPF101800"]
        case_data_071_QAPF100300 = self.test_data["071_QAPF100300"]
        case_data_073_QAPF100500 = self.test_data["073_QAPF100500"]
        case_data_075_QAPF100500 = self.test_data["075_QAPF100500"]
        case_data_077_QAPF100500 = self.test_data["077_QAPF100500"]
        case_data_079_QAPF100500 = self.test_data["079_QAPF100500"]
        case_data_081_QAPF100500 = self.test_data["081_QAPF100500"]
        case_data_083_QAPF100500 = self.test_data["083_QAPF100500"]
        case_data_085_QAPF100500 = self.test_data["085_QAPF100500"]
        case_data_087_QAPF100500 = self.test_data["087_QAPF100500"]
        case_data_091_QAPF100500 = self.test_data["091_QAPF100500"]
        tab_index = 0

        self.do_login_new_tab()

        # 33 メインメニュー 画面: 「子ども子育て支援」ボタン押下
        # 34 メインメニュー 画面: 「世帯情報」ボタン押下
        # 35 メインメニュー 画面: 「検索」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=True)
        tab_index += 1

        # 36 検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                value=case_data_036_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
                text=case_data_036_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                value=case_data_036_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                text=case_data_036_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                value=case_data_036_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                value=case_data_036_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
                text=case_data_036_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                text=case_data_036_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                value=case_data_036_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                value=case_data_036_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                value=case_data_036_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                value=case_data_036_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                value=case_data_036_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                text=case_data_036_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                value=case_data_036_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                value=case_data_036_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力 画面_36")

        # 37 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 38 世帯履歴 画面: 対象の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_038_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code = atena_code, tab_index = tab_index)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳 画面_38")

        # 39 世帯台帳 画面:「認定情報」タブを押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100300", label="認定情報")
        self.screen_shot("世帯台帳_認定情報 画面_39")

        # 40 世帯台帳 画面:「保護者認定」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100300_btnHogoshaNintei_button")

        # 41 保護者認定 画面: 「修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100800_btnEditChg_button")

        # 42 保護者認定 画面: 修正情報を入力する
        # 申請年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100800_txtNinteiINFOInputShinseiYMD_textboxInput",
                value=case_data_042_QAPF100800.get("shinsei_nen_gappi", ""))
        # 保護者1氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100800_selNinteiINFOInputHogosha1ShimeiSentaku_select",
                text=case_data_042_QAPF100800.get("hogosha1_shimei", ""))
        # 決定年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100800_txtNinteiINFOInputKetteiYMD_textboxInput",
                value=case_data_042_QAPF100800.get("kettei_nen_gappi", ""))
        # 保護者2氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100800_selNinteiINFOInputHogosha2ShimeiSentaku_select",
                text=case_data_042_QAPF100800.get("hogosha2_shimei", ""))
        # 認定年月
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100800_txtNinteiINFOInputNinteiYMStart_textboxInput",
                value=case_data_042_QAPF100800.get("nintei_nengappi", ""))
        # 変更理由
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100800_selNinteiINFOInputHenkoRiyu_select",
                text=case_data_042_QAPF100800.get("henko_riyu", ""))
        self.screen_shot("保護者認定 画面_42")

        # 43 保護者認定 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100800_regbtn_button")

        # 44 保護者認定 画面: 「登録してもよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF100800_msg_span",
                                 msg="更新しました。")
        self.screen_shot("保護者認定 画面_44")

        # 45 保護者認定 画面: パンくず「世帯台帳」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="QAPF100800", label="世帯台帳")

        # 46 世帯台帳 画面:「納付義務者認定」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100300_btnNogimushaNintei_button")

        # 47 納付義務者認定 画面:「修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100900_btnEditChg_button")

        # 48 納付義務者認定 画面: 修正情報を入力する
        # 決定年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100900_txtNinteiINFOInputKetteiYMD_textboxInput",
                value=case_data_048_QAPF100900.get("kettei_nengappi", ""))
        # 申請年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100900_txtNinteiINFOInputShinseiYMD_textboxInput",
                value=case_data_048_QAPF100900.get("shinsei_nengappi", ""))
        # 認定年月開始
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100900_txtNinteiINFOInputNinteiYMStart_textboxInput",
                value=case_data_048_QAPF100900.get("nintei_nengetsu_kaishi", ""))
        # 納付義務者１氏名選択
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100900_selNinteiINFOInputNofuGimusha1ShimeiSentaku_select",
                text=case_data_048_QAPF100900.get("nofu_gimusha1_shimei_sentaku", ""))
        # 納付義務者2氏名選択
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100900_selNinteiINFOInputNofuGimusha2ShimeiSentaku_select",
                text=case_data_048_QAPF100900.get("nofu_gimusha2_shimei_sentaku", ""))
        # 変更理由
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100900_selNinteiINFOInputHenkoRiyu_select",
                text=case_data_048_QAPF100900.get("henko_riyuu", ""))
        self.screen_shot("納付義務者認定 画面_48")

        # 49 納付義務者認定 画面: 「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100900_regbtn_button")

        # 50 納付義務者認定 画面:「登録してもよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF100900_msg_span",
                                 msg="更新しました。")
        self.screen_shot("納付義務者認定 画面_50")

        # 51 納付義務者認定 画面: パンくず「世帯台帳」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="QAPF100900", label="世帯台帳")

        # 52 世帯台帳 画面:「生保認定」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100300_btnSeihoNintei_button")

        # 53 生保認定 画面:「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF101200_btnAddChg_button")

        # 54 生保認定 画面: 生保認定に関する情報を入力する
        # ケース番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101200_txtCaseNo_textboxInput",
                value=case_data_054_QAPF101200.get("case_bango", ""))
        # 決定年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101200_txtNinteiJohoInputKetteiYMD_textboxInput",
                value=case_data_054_QAPF101200.get("kettei_nengappi", ""))
        # 申請年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101200_txtNinteiJohoInputShinseiYMD_textboxInput",
                value=case_data_054_QAPF101200.get("shinsei_nengappi", ""))
        # 認定年月開始
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101200_txtNinteiJohoInputKaishi_textboxInput",
                value=case_data_054_QAPF101200.get("nintei_nengetsu_kaishi", ""))
        # 認定年月終了
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101200_txtNinteiJohoInputSyuryo_textboxInput",
                value=case_data_054_QAPF101200.get("nintei_nengetsu_shuryo", ""))
        # 保護開始日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101200_txtHogoKaishibi_textboxInput",
                value=case_data_054_QAPF101200.get("hogo_kaishi_hi", ""))
        # 保護終了日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101200_txtHogoShuryobi_textboxInput",
                value=case_data_054_QAPF101200.get("hogo_shuryo_hi", ""))
        self.screen_shot("生保認定 画面_54")

        # 55 生保認定 画面: 「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF101200_regbtn_button")

        # 56 生保認定 画面: 「登録してもよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF101200_msg_span",
                                 msg="登録しました。")
        self.screen_shot("生保認定 画面_56")

        # 57 生保認定 画面: パンくず「世帯台帳」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="QAPF101200", label="世帯台帳")

        # 58 世帯台帳 画面:「ひとり親認定」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100300_btnHitorioyaNintei_button")

        # 59 ひとり親認定 画面:「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF101300_btnAddChg_button")

        # 60 ひとり親認定 画面: ひとり親認定に関する情報を入力する
        # ひとり親区分
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101300_selNinteiINFOInputHitorioyaKbn_select",
                text=case_data_060_QAPF101300.get("hitorioya_kubun", ""))
        # 決定年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101300_txtNinteiINFOInputKetteiYMD_textboxInput",
                value=case_data_060_QAPF101300.get("kettei_nengappi", ""))
        # 申請年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101300_txtNinteiINFOInputShinseiYMD_textboxInput",
                value=case_data_060_QAPF101300.get("shinsei_nengappi", ""))
        # 認定年月開始
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101300_txtNinteiINFOInputNinteiYMStart_textboxInput",
                value=case_data_060_QAPF101300.get("nintei_nengetsu_kaishi", ""))
        # 認定年月終了
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101300_txtNinteiINFOInputNinteiYMEnd_textboxInput",
                value=case_data_060_QAPF101300.get("nintei_nengetsu_shuryo", ""))
        self.screen_shot("ひとり親認定 画面_60")

        # 61 ひとり親認定 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF101300_regbtn_button")

        # 62 ひとり親認定 画面:「登録してもよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF101300_msg_span",
                                 msg="登録しました。")
        self.screen_shot("ひとり親認定 画面_62")

        # 63 ひとり親認定 画面: パンくず「世帯台帳」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="QAPF101300", label="世帯台帳")

        # 64 世帯台帳 画面:「障がい世帯認定」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100300_btnShogaiSetaiNintei_button")

        # 65 障がい世帯認定 画面:「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF101800_btnAddChg_button")

        # 66 障がい世帯認定 画面: ひとり親認定に関する情報を入力する
        # 決定年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101800_txtNinteiINFOInputKetteiYMD_textboxInput",
                value=case_data_066_QAPF101800.get("kettei_nengappi", ""))
        # 申請年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101800_txtNinteiINFOInputShinseiYMD_textboxInput",
                value=case_data_066_QAPF101800.get("shinsei_nengappi", ""))
        # 認定年月開始
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101800_txtNinteiINFOInputNinteiYMStart_textboxInput",
                value=case_data_066_QAPF101800.get("nintei_nengetsu_kaishi", ""))
        # 認定年月終了
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF101800_txtNinteiINFOInputNinteiYMEnd_textboxInput",
                value=case_data_066_QAPF101800.get("nintei_nengetsu_shuryo", ""))
        self.screen_shot("障がい世帯認定 画面_66")

        # 67 障がい世帯認定 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF101800_regbtn_button")

        # 68 障がい世帯認定 画面:「登録してもよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF101800_msg_span",
                                 msg="登録しました。")
        self.screen_shot("障がい世帯認定 画面_68")

        # 69 障がい世帯認定 画面: パンくず「世帯台帳」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="QAPF101800", label="世帯台帳")

        # 70 世帯台帳 画面:「世帯員一覧」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100300", label="世帯員一覧")
        self.screen_shot("世帯台帳_世帯員一覧 画面_70")

        # 71 世帯台帳 画面: 対象の「№」ボタン押下
        self.click_setaiin_by_atena_code(atena_code=case_data_071_QAPF100300.get("atena_code", ""), tab_index=tab_index)

        # 72 世帯員詳細情報管理 画面: 「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100500_btnAddChg_button")

        # 73 世帯員詳細情報管理 画面:「認定年月日」を入力する
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiNinteiDayNinteiYMD_textboxInput",
                value=case_data_073_QAPF100500.get("nintei_nen_gappi", ""))
        self.screen_shot("世帯員詳細情報管理 画面_73")

        # 74 世帯員詳細情報管理 画面:「基本情報」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="基本情報")

        # 75 世帯員詳細情報管理 画面: 基本情報を入力する
        # 健康状態
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKihonKenkoJotai_select",
                text=case_data_075_QAPF100500.get("kenko_jotai", ""))
        # 他施設利用
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKihonTaShisetsuRiyo_select",
                text=case_data_075_QAPF100500.get("hoka_shisetsu_riyo", ""))
        # その他特記事項
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKihonSoNotaTokkijiko_textboxInput",
                value=case_data_075_QAPF100500.get("sonota_tokkijiko", ""))
        # 備考
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKihonBiko_textarea",
                value=case_data_075_QAPF100500.get("biko", ""))
        # 児童状況
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKihonHanyoKomokuSentaku_select",
                text=case_data_075_QAPF100500.get("jido_jokyo", ""))
        # 汎用項目２
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKihonHanyoKomokuSentaku2_select",
                text=case_data_075_QAPF100500.get("hanyo_komoku2", ""))
        # 汎用項目テキスト1
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKihonHanyoKomokuInput_textboxInput",
                value=case_data_075_QAPF100500.get("hanyo_komoku_text1", ""))
        # 汎用項目テキスト2
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKihonHanyoKomokuInput2_textboxInput",
                value=case_data_075_QAPF100500.get("hanyo_komoku_text2", ""))
        self.screen_shot("世帯員詳細情報管理_基本情報 画面_75")

        # 76 世帯員詳細情報管理 画面:「アレルギー情報」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="アレルギー情報")

        # 77 世帯員詳細情報管理 画面: アレルギー情報を入力する
        # アレルギー_卵
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk0",
                value=case_data_077_QAPF100500.get("tamago", ""))
        # アレルギー_小麦
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk1",
                value=case_data_077_QAPF100500.get("komugi", ""))
        # アレルギー_えび
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk2",
                value=case_data_077_QAPF100500.get("ebi", ""))
        # アレルギー_かに
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk3",
                value=case_data_077_QAPF100500.get("kani", ""))
        # アレルギー_そば
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk4",
                value=case_data_077_QAPF100500.get("soba", ""))
        # アレルギー_落花生
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk5",
                value=case_data_077_QAPF100500.get("rakka_sei", ""))
        # アレルギー_乳
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk6",
                value=case_data_077_QAPF100500.get("nyu", ""))
        # アレルギー_あわび
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk7",
                value=case_data_077_QAPF100500.get("awabi", ""))
        # アレルギー_いか
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk8",
                value=case_data_077_QAPF100500.get("ika", ""))
        # アレルギー_いくら
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk9",
                value=case_data_077_QAPF100500.get("ikura", ""))
        # アレルギー_オレンジ
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk10",
                value=case_data_077_QAPF100500.get("orenji", ""))
        # アレルギー_キウイフルーツ
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk11",
                value=case_data_077_QAPF100500.get("kiwi_fruit", ""))
        # アレルギー_牛肉
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk12",
                value=case_data_077_QAPF100500.get("gyuniku", ""))
        # アレルギー_くるみ
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk13",
                value=case_data_077_QAPF100500.get("kurumi", ""))
        # アレルギー_さけ
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk14",
                value=case_data_077_QAPF100500.get("sake", ""))
        # アレルギー_さば
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk15",
                value=case_data_077_QAPF100500.get("saba", ""))
        # アレルギー_大豆
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk16",
                value=case_data_077_QAPF100500.get("daizu", ""))
        # アレルギー_鶏肉
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk17",
                value=case_data_077_QAPF100500.get("toriniku", ""))
        # アレルギー_バナナ
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk18",
                value=case_data_077_QAPF100500.get("banana", ""))
        # アレルギー_豚肉
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk19",
                value=case_data_077_QAPF100500.get("butaniku", ""))
        # アレルギー_まつたけ
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk20",
                value=case_data_077_QAPF100500.get("matsutake", ""))
        # アレルギー_もも
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk21",
                value=case_data_077_QAPF100500.get("momo", ""))
        # アレルギー_やまいも
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk22",
                value=case_data_077_QAPF100500.get("yamaimo", ""))
        # アレルギー_りんご
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk23",
                value=case_data_077_QAPF100500.get("ringo", ""))
        # アレルギー_ゼラチン
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiArerugiHanyoKomokuSentakuchk24",
                value=case_data_077_QAPF100500.get("zerachin", ""))
        # 備考
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiArerugiSoNotaBiko_textarea",
                value=case_data_077_QAPF100500.get("biko", ""))
        self.screen_shot("世帯員詳細情報管理_アレルギー情報 画面_77")

        # 78 世帯員詳細情報管理 画面:「就労」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="就労")

        # 79 世帯員詳細情報管理 画面: 就労情報を入力する
        for i in range(3):
            self.click_button_by_label(f"{i + 1}")
            # 勤務先種別
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKimmusakiINFOKimmusakiShubetsu_select",
                    text=case_data_079_QAPF100500.get(f"kinmusaki_shubetsu_{i + 1}", ""))
            # 勤務先名
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOKimmusakiNM_textboxInput",
                    value=case_data_079_QAPF100500.get(f"kinmusaki_mei_{i + 1}", ""))
            # 勤務先住所
            # Checkbox 直接入力
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiKimmusakiINFOChokusetsuInputchk0",
                    value="1")
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOKimmusakiJusho_textboxInput",
                    value=case_data_079_QAPF100500.get(f"kinmusaki_jusho_{i + 1}", ""))
            # 勤務先方書
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOKatagaki_textboxInput",
                    value=case_data_079_QAPF100500.get(f"kinmusaki_katagaki_{i + 1}", ""))
            # 業務内容
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOGyomuNaiyo_textboxInput",
                    value=case_data_079_QAPF100500.get(f"gyomu_naiyo_{i + 1}", ""))
            # 勤務開始年月日
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOKimmuStartYMD_textboxInput",
                    value=case_data_079_QAPF100500.get(f"kinmu_kaishi_nen_gappi_{i + 1}", ""))
            # 勤務終了年月日
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOKimmuEndYMD_textboxInput",
                    value=case_data_079_QAPF100500.get(f"kinmu_shuryo_nen_gappi_{i + 1}", ""))
            # 勤務形態
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKimmusakiINFOKimmuKeitai_select",
                    text=case_data_079_QAPF100500.get(f"kinmu_keitai_select{i + 1}", ""))
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOKimmuKeitaiInput_textboxInput",
                    value=case_data_079_QAPF100500.get(f"kinmu_keitai_input_{i + 1}", ""))
            # 就労曜日・時間_月
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuroYoubiMon_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_yobi_jikan_getsuyo_{i + 1}", ""))
            # 就労曜日・時間_火
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuroYoubiTue_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_yobi_jikan_kayo_{i + 1}", ""))
            # 就労曜日・時間_水
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuroYoubiWed_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_yobi_jikan_suiyo_{i + 1}", ""))
            # 就労曜日・時間_木
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuroYoubiThu_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_yobi_jikan_mokuyo_{i + 1}", ""))
            # 就労曜日・時間_金
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuroYoubiFri_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_yobi_jikan_kinyo_{i + 1}", ""))
            # 就労曜日・時間_土
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuroYoubiSat_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_yobi_jikan_doyo_{i + 1}", ""))
            # 就労曜日・時間_日
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuroYoubiSun_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_yobi_jikan_nichiyo_{i + 1}", ""))
            # 就労日数_日/週
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOWeekShuroNissuu_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_nissu_hi_shu_{i + 1}", ""))
            # 就労日数_日/月
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOMonthShuroNissuu_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_nissu_hi_getsu_{i + 1}", ""))
            # 週就労時間
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuShuroTime_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shu_shuro_jikan_{i + 1}", ""))
            # 月就労時間
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOMonthShuroTime_textboxInput",
                    value=case_data_079_QAPF100500.get(f"getsu_shuro_jikan_{i + 1}", ""))
            # 通勤時間
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOTsukinTime_textboxInput",
                    value=case_data_079_QAPF100500.get(f"tsukin_jikan_{i + 1}", ""))
            # 通勤時間_時間
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKimmusakiINFOTsukinTimeSentaku_select",
                    text=case_data_079_QAPF100500.get(f"tsukin_jikan_jikan_{i + 1}", ""))
            # 通勤経路
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOTsukinKeiro_textboxInput",
                    value=case_data_079_QAPF100500.get(f"tsukin_keiro_{i + 1}", ""))
            # 就労実績
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOShuroJisseki_textboxInput",
                    value=case_data_079_QAPF100500.get(f"shuro_jisseki_{i + 1}", ""))
            # 備考
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOBiko_textboxInput",
                    value=case_data_079_QAPF100500.get(f"biko_{i + 1}", ""))
            # 汎用項目３
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKimmusakiHanyoKomokuSentaku3_select",
                    text=case_data_079_QAPF100500.get(f"hanyo_komoku_3_{i + 1}", ""))
            # 汎用項目テキスト3
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiHanyoKomokuInput3_textboxInput",
                    value=case_data_079_QAPF100500.get(f"hanyo_komoku_text_3_{i + 1}", ""))
            # 汎用項目４
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiKimmusakiHanyoKomokuSentaku4_select",
                    text=case_data_079_QAPF100500.get(f"hanyo_komoku_4_{i + 1}", ""))
            # 汎用項目テキスト4
            self.form_input_by_id(
                    idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiHanyoKomokuInput4_textboxInput",
                    value=case_data_079_QAPF100500.get(f"hanyo_komoku_text_4_{i + 1}", ""))
            self.screen_shot(f"世帯員詳細情報管理_就労 画面_79_{i + 1}")

        self.click_by_id("tab0" + str(tab_index) + "_QAPF100500_btnSetainShosaiKimmusakiINFOSum_button")
        self.screen_shot("世帯員詳細情報管理_就労 画面_79")

        # 80 世帯員詳細情報管理 画面:「妊娠出産・育休」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="妊娠出産・育休")

        # 81 世帯員詳細情報管理 画面: 妊娠出産・育休情報を入力する
        # 出産予定日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoShussanYoteibi_textboxInput",
                value=case_data_081_QAPF100500.get("shussan_yotei_bi", ""))
        # 出産年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoShussanbi_textboxInput",
                value=case_data_081_QAPF100500.get("shussan_nen_gappi", ""))
        # 育児短時間
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOIkujitanjiKan_textboxInput",
                value=case_data_081_QAPF100500.get("ikuji_tanjikan", ""))
        # 育児時間
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOIkujiTime_textboxInput",
                value=case_data_081_QAPF100500.get("ikuji_jikan", ""))
        # 育児短時間・期間_開始
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOIkujitanjiKanKikanS_textboxInput",
                value=case_data_081_QAPF100500.get("ikuji_tanjikan_kikan_kaishi", ""))
        # 育児短時間・期間_終了
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOIkujitanjiKanKikanE_textboxInput",
                value=case_data_081_QAPF100500.get("ikuji_tanjikan_kikan_shuryo", ""))
        # 産休育休取得状況_開始
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOSankyuIkukyuGetJoSt_textboxInput",
                value=case_data_081_QAPF100500.get("sankyu_ikukyu_shutoku_jokyo_kaishi", ""))
        # 産休育休取得状況_終了
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKimmusakiINFOSankyuIkukyuGetJoEn_textboxInput",
                value=case_data_081_QAPF100500.get("sankyu_ikukyu_shutoku_jokyo_shuryo", ""))
        # 備考
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiNinsinShussanIkujiBiko_textarea",
                value=case_data_081_QAPF100500.get("biko", ""))
        self.screen_shot("世帯員詳細情報管理_妊娠出産・育休 画面_81")

        # 82 世帯員詳細情報管理 画面:「障がい・疾病」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="障がい・疾病")

        # 83 世帯員詳細情報管理 画面: 障がい・疾病情報を入力する
        # 障がい区分
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiShussanKaigoShogaiKbn_select",
                text=case_data_083_QAPF100500.get("shogai_kubun", ""))
        # 決定年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoShussanKetteibi_textboxInput",
                value=case_data_083_QAPF100500.get("kettei_nen_gappi", ""))
        # 手帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoShussanTetyouNo_textboxInput",
                value=case_data_083_QAPF100500.get("techo_bango", ""))
        # 総合等級
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiShussanKaigoSogoKyu_select",
                text=case_data_083_QAPF100500.get("sogo_tokyu", ""))
        # 障がい名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiShussanKaigoDaihyoShogaiNM_textboxInput",
                value=case_data_083_QAPF100500.get("shogai_mei", ""))
        # 疾病名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoShippeiNM_textboxInput",
                value=case_data_083_QAPF100500.get("shippei_mei", ""))
        # 入院通院区分
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiShussanKaigoNyuinTsuinKbn_select",
                text=case_data_083_QAPF100500.get("nyuin_tsuin_kubun", ""))
        # 通院曜日_月
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoTsuinYoubichk0",
                value=case_data_083_QAPF100500.get("tsuin_yobi_getsuyo", ""))
        # 通院曜日_火
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoTsuinYoubichk1",
                value=case_data_083_QAPF100500.get("tsuin_yobi_kayo", ""))
        # 通院曜日_水
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoTsuinYoubichk2",
                value=case_data_083_QAPF100500.get("tsuin_yobi_suiyo", ""))
        # 通院曜日_木
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoTsuinYoubichk3",
                value=case_data_083_QAPF100500.get("tsuin_yobi_mokuyo", ""))
        # 通院曜日_金
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoTsuinYoubichk4",
                value=case_data_083_QAPF100500.get("tsuin_yobi_kinyo", ""))
        # 通院曜日_土
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoTsuinYoubichk5",
                value=case_data_083_QAPF100500.get("tsuin_yobi_doyo", ""))
        # 通院曜日_日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoTsuinYoubichk6",
                value=case_data_083_QAPF100500.get("tsuin_yobi_nichiyo", ""))
        # 通院曜日_不定期
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoTsuinYoubiFuteikichk0",
                value=case_data_083_QAPF100500.get("tsuin_yobi_futeiki", ""))
        # 通院曜日_日/週
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoTsuinYoubiShu_textboxInput",
                value=case_data_083_QAPF100500.get("tsuin_nissu_hi_shu", ""))
        # 備考
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShogaiShippeiBiko_textarea",
                value=case_data_083_QAPF100500.get("biko", ""))
        # 汎用項目5
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiShussanKaigoaHanyoKomokuSentaku5_select",
                text=case_data_083_QAPF100500.get("hanyo_komoku_5", ""))
        # 汎用項目テキスト5
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoHanyoKomokuInput5_textboxInput",
                value=case_data_083_QAPF100500.get("hanyo_komoku_text_5", ""))
        self.screen_shot("世帯員詳細情報管理_障がい・疾病 画面_83")

        # 84 世帯員詳細情報管理 画面:「介護」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="介護")

        # 85 世帯員詳細情報管理 画面: 介護情報を入力する
        # 被介護者名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoHiKaigosyaNM_textboxInput",
                value=case_data_085_QAPF100500.get("hi_kaigosha_mei", ""))
        # 要介護度
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiShussanKaigoYouKaigodo_select",
                text=case_data_085_QAPF100500.get("yo_kaigodo", ""))
        # 居宅区分
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiShussanKaigoKyotakKbn_select",
                text=case_data_085_QAPF100500.get("kyotaku_kubun", ""))
        # 介護就労曜日_月
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoKaigoShuroYoubiMon_textboxInput",
                value=case_data_085_QAPF100500.get("kaigo_shuro_yobi_getsuyo", ""))
        # 介護就労曜日_火
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoKaigoShuroYoubiTue_textboxInput",
                value=case_data_085_QAPF100500.get("kaigo_shuro_yobi_kayo", ""))
        # 介護就労曜日_水
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoKaigoShuroYoubiWed_textboxInput",
                value=case_data_085_QAPF100500.get("kaigo_shuro_yobi_suiyo", ""))
        # 介護就労曜日_木
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoKaigoShuroYoubiThu_textboxInput",
                value=case_data_085_QAPF100500.get("kaigo_shuro_yobi_mokuyo", ""))
        # 介護就労曜日_金
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoKaigoShuroYoubiFri_textboxInput",
                value=case_data_085_QAPF100500.get("kaigo_shuro_yobi_kinyo", ""))
        # 介護就労曜日_土
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoKaigoShuroYoubiSat_textboxInput",
                value=case_data_085_QAPF100500.get("kaigo_shuro_yobi_doyo", ""))
        # 介護就労曜日_日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoKaigoShuroYoubiSun_textboxInput",
                value=case_data_085_QAPF100500.get("kaigo_shuro_yobi_nichiyo", ""))
        # 介護就労曜日_不定期
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiShussanKaigoKaigoShuroYoubiFuteikichk0",
                value=case_data_085_QAPF100500.get("kaigo_shuro_yobi_futeiki", ""))
        # 不定期_日/週
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKaigoShuroNissuWeek_textboxInput",
                value=case_data_085_QAPF100500.get("futeiki_hi_shu", ""))
        # 不定期_日/月
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKaigoShuroNissuMonth_textboxInput",
                value=case_data_085_QAPF100500.get("futeiki_hi_getsu", ""))
        # 週就労時間
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKaigoWeekShuroTime_textboxInput",
                value=case_data_085_QAPF100500.get("shu_shuro_jikan", ""))
        # 月就労時間
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKaigoMonthShuroTime_textboxInput",
                value=case_data_085_QAPF100500.get("getsu_shuro_jikan", ""))
        # 備考
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKaigoBiko_textarea",
                value=case_data_085_QAPF100500.get("biko", ""))
        # 汎用項目6
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiShussanKaigoaHanyoKomokuSentaku6_select",
                text=case_data_085_QAPF100500.get("hanyo_komoku_6", ""))
        # 汎用項目テキスト6
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiShussanKaigoHanyoKomokuInput6_textboxInput",
                value=case_data_085_QAPF100500.get("hanyo_komoku_text_6", ""))
        self.screen_shot("世帯員詳細情報管理_介護 画面_85")

        # 86 世帯員詳細情報管理 画面:「求職」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="求職")

        # 87 世帯員詳細情報管理 画面: 求職情報を入力する
        # 求職区分
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaKyushokuKyushokuKbn_select",
                text=case_data_087_QAPF100500.get("kyushoku_kubun", ""))
        # 就労曜日・時間_月
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroYoubiMon_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_yobi_jikan_getsuyo", ""))
        # 就労曜日・時間_火
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroYoubiTue_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_yobi_jikan_kayo", ""))
        # 就労曜日・時間_水
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroYoubiWed_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_yobi_jikan_suiyo", ""))
        # 就労曜日・時間_木
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroYoubiThu_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_yobi_jikan_mokuyo", ""))
        # 就労曜日・時間_金
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroYoubiFri_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_yobi_jikan_kinyo", ""))
        # 就労曜日・時間_土
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroYoubiSat_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_yobi_jikan_doyo", ""))
        # 就労曜日・時間_日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroYoubiSun_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_yobi_jikan_nichiyo", ""))
        # 就労曜日・時間_不定期
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_chkSetainShosaiKyushokuShuroYoubiFuteikichk0",
                value=case_data_087_QAPF100500.get("shuro_yobi_jikan_futeiki", ""))
        # 就労日数_日/週
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroNissuWeek_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_nissu_hi_shu", ""))
        # 就労日数_日/月
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuShuroNissuMonth_textboxInput",
                value=case_data_087_QAPF100500.get("shuro_nissu_hi_getsu", ""))
        # 週就労時間
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuWeekShuroTime_textboxInput",
                value=case_data_087_QAPF100500.get("shu_shuro_jikan", ""))
        # 月就労時間
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuMonthShuroTime_textboxInput",
                value=case_data_087_QAPF100500.get("getsu_shuro_jikan", ""))
        # 備考
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiKyushokuBiko_textarea",
                value=case_data_087_QAPF100500.get("biko", ""))
        self.screen_shot("世帯員詳細情報管理_求職 画面_87")

        # 88 世帯員詳細情報管理 画面:「各種情報照会」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="各種情報照会")

        # 89 世帯員詳細情報管理 画面: 内容を確認する。
        self.screen_shot("世帯員詳細情報管理_各種情報照会 画面_89")

        # 90 世帯員詳細情報管理 画面:「その他情報」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100500", label="その他情報")

        # 91 世帯員詳細情報管理 画面: その他情報を入力する
        # 汎用項目７
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiSoNotaHanyoKomokuSentaku7_select",
                text=case_data_091_QAPF100500.get("hanyo_komoku_7", ""))
        # 汎用項目テキスト7
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiSoNotaHanyoKomokuInput7_textboxInput",
                value=case_data_091_QAPF100500.get("hanyo_komoku_text_7", ""))
        # 汎用項目８
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiSoNotaHanyoKomokuSentaku8_select",
                text=case_data_091_QAPF100500.get("hanyo_komoku_8", ""))
        # 汎用項目テキスト8
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiSoNotaHanyoKomokuInput8_textboxInput",
                value=case_data_091_QAPF100500.get("hanyo_komoku_text_8", ""))
        # 汎用項目９
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiSoNotaHanyoKomokuSentaku9_select",
                text=case_data_091_QAPF100500.get("hanyo_komoku_9", ""))
        # 汎用項目テキスト9
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_txtSetainShosaiSoNotaHanyoKomokuInput9_textboxInput",
                value=case_data_091_QAPF100500.get("hanyo_komoku_text_9", ""))
        # 汎用項目10
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF100500_selSetainShosaiSoNotaHanyoKomokuSentaku10_select",
                text=case_data_091_QAPF100500.get("hanyo_komoku_10", ""))
        self.screen_shot("世帯員詳細情報管理_その他情報 画面_91")

        # 92 世帯員詳細情報管理 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100500_regbtn_button")

        # 93 世帯員詳細情報管理 画面:「登録してもよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF100500_msg_span",
                                 msg="登録しました。")
        self.screen_shot("世帯員詳細情報管理 画面_93")
