import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

#転用元シナリオ:TestQAG030_01090113 
class TestQAG030_01090327(FukushiSiteTestCaseBase):
    """TestQAG030_01090327"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 調査書を出力できることを確認する。
    def test_QAG030_01090327(self):
        """調査書作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG030")
        # 1 帳票印刷画面: 表示
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("印刷")
        self.screen_shot("帳票印刷画面_1")

        # 2 帳票印刷画面: 「調査書」行の印刷チェックボックス選択「調査書」行の発行年月日チェックボックス選択発行年月日「○○」入力
        #self.print_online_reports(case_name="帳票印刷画面", report_name="調査書", hakkou_ymd="20240701")
        self.click_by_id(idstr="insatsuChk_1")
        #変更-自己負担限度額変更の住民↓
        self.form_input_by_id(idstr="TxtHakkoYMD", value=case_data.get("hakkou_ymd_henko_jikofutan", ""))
        self.click_button_by_label("一括反映")
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        self.alert_ok()

        # 4 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("帳票印刷_4")

        # 5 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_5")

        # 6 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 7 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 8 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_8")
