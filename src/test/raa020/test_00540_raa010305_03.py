import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00540RAA01030503(FukushiSiteTestCaseBase):
    """TEST00540RAA01030503"""
    
    def test_00540_raa010305_03(self):
        """test_00540_raa010305_03"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        
        # ログイン　療育手帳
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA020")
        self.screen_shot("raa010305_03-01")
        
        self.click_button_by_label("次頁")

        self.click_button_by_label("印刷")
        self.screen_shot("raa010305_03-02")
        # 「知的障害者更生指導台帳（指導記録）」行の印刷チェックボックス選択
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        self.screen_shot("raa010305_03-06")
        
        # 「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("raa010305_03-09")