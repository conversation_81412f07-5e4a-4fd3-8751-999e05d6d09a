import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG010_01080105(FukushiSiteTestCaseBase):
    """TestQAG010_01080105"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 申請済の住民に対し判定依頼登録ができることを確認する
    def test_QAG010_01080105(self):
        """判定依頼入力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shintatsu1_ymd = case_data.get("shintatsu1_ymd", "")
        hantei_ymd = case_data.get("hantei_ymd", "")
        hantei_yotei_ji = case_data.get("hantei_yotei_ji", "")
        hantei_yotei_fun = case_data.get("hantei_yotei_fun", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG010")
        # 1 自立支援医療(更生医療)資格管理: 「判定入力」ボタン押下
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("判定入力")

        # 2 自立支援医療(更生医療)資格管理: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理_2")

        # 3 自立支援医療(更生医療)資格管理: 判定依頼年月日「20230701」入力判定予定日「20230701」入力判定予定時間「10」:「00」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shintatsu1_ymd)
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=shintatsu1_ymd)
        self.form_input_by_id(idstr="TxtHanteiYMD", value=hantei_ymd)
        self.form_input_by_id(idstr="TxtHanteiYotei_Ji", value=hantei_yotei_ji)
        self.form_input_by_id(idstr="TxtHanteiYotei_Fun", value=hantei_yotei_fun)
        self.form_input_by_id(idstr="TxtUketsukeBango", value="")
        # 4 自立支援医療(更生医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 自立支援医療(更生医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(更生医療)資格管理_5")
