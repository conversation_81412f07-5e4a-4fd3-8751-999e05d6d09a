import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QMDF063(FukushiSiteTestCaseBase):
    """Test_QMDF063"""

    def setUp(self):
        self.exec_sqlfile("QMDF063_実行前スクリプト.sql")        
        super().setUp() 
        
    def tearDown(self):
        self.exec_sqlfile("QMDF061-QMDF063_実行後スクリプト.sql")
        super().tearDown()

    def test_case_001(self):
        """test_case_001"""
        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.click_button_by_label("汎用地区マスタメンテナンス")
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "BtnKakutei").click()
        self.driver.find_element(By.ID, "span_BtnTsuika").click()
        self.screen_shot("QMDF063_1",caption="汎用区域マスタ登録_初期表示")
        self.driver.find_element(By.ID, "CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "span_CmdKTsuika").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000014")
        self.driver.find_element(By.ID, "TxtKuikiKana").click()
        self.driver.find_element(By.ID, "TxtKuikiKana").send_keys("ｼｼﾎﾞﾈｲｯﾁｮｳﾒ")
        self.driver.find_element(By.ID, "TxtKuikiKanji").click()
        self.driver.find_element(By.ID, "TxtKuikiKanji").send_keys("鹿骨一丁目")
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").send_keys("令和05年12月05日")
        self.driver.find_element(By.ID, "span_CmdATsuika").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").send_keys("010001000")
        self.driver.find_element(By.ID, "TxtEndJushoCode").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("010001000")
        self.driver.find_element(By.ID, "span_CmdNKanryo").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "BtnKakutei").click()
        self.driver.find_element(By.ID, "span_BtnTsuika").click()
        self.driver.find_element(By.ID, "span_CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "span_CmdKTsuika").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000013")
        self.driver.find_element(By.ID, "TxtKuikiKana").click()
        self.driver.find_element(By.ID, "TxtKuikiKana").send_keys("ｼｼﾎﾞﾈｻﾝﾁｮｳﾒ")
        self.driver.find_element(By.ID, "TxtKuikiKanji").click()
        self.driver.find_element(By.ID, "TxtKuikiKanji").send_keys("鹿骨三丁目")
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").send_keys("令和05年12月05日")
        self.driver.find_element(By.ID, "span_CmdATsuika").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").send_keys("010001000")
        self.driver.find_element(By.ID, "TxtEndJushoCode").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("010001000")
        self.driver.find_element(By.ID, "span_CmdNKanryo").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "span_CmdKTsuika").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000012")
        self.driver.find_element(By.ID, "TxtKuikiKana").click()
        self.driver.find_element(By.ID, "TxtKuikiKana").send_keys("ｱｷﾀｹﾝ ﾜｹｲｻﾂｼｮ")
        self.driver.find_element(By.ID, "TxtKuikiKanji").click()
        self.driver.find_element(By.ID, "TxtKuikiKanji").send_keys("秋田県警察署")
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").send_keys("令和05年11月10日")
        self.driver.find_element(By.ID, "span_CmdATsuika").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").send_keys("010001000")
        self.driver.find_element(By.ID, "TxtEndJushoCode").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("010001000")
        self.driver.find_element(By.ID, "span_CmdNKanryo").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "BtnKakutei").click()
        self.driver.find_element(By.ID, "BtnTsuika").click()
        self.driver.find_element(By.ID, "span_CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "span_CmdKTsuika").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000012")
        self.driver.find_element(By.ID, "TxtKuikiKana").click()
        self.driver.find_element(By.ID, "TxtKuikiKana").send_keys("ｱｷﾀｹﾝ ﾜｹｲｻﾂｼｮ")
        self.driver.find_element(By.ID, "TxtKuikiKanji").click()
        self.driver.find_element(By.ID, "TxtKuikiKanji").send_keys("秋田県警察署")
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").send_keys("令和05年12月01日")
        self.driver.find_element(By.ID, "span_CmdATsuika").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").send_keys("010001000")
        self.driver.find_element(By.ID, "TxtEndJushoCode").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("010001000")
        self.driver.find_element(By.ID, "span_CmdNKanryo").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF063_2",caption="汎用区域マスタ登録_※3の「登録」ボタン押下")

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "BtnKakutei").click()
        self.driver.find_element(By.ID, "BtnTsuika").click()
        self.driver.find_element(By.ID, "span_CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000012")
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.driver.find_element(By.ID, "span_Sel1").click()
        self.driver.find_element(By.ID, "span_CmdKShusei").click()
        self.driver.find_element(By.ID, "ASel1").click()
        self.driver.find_element(By.ID, "span_CmdAShusei").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("010002000")
        self.driver.find_element(By.ID, "span_CmdNKanryo").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF063_3",caption="汎用区域マスタ登録_※4の「登録」ボタン押下")

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "BtnKakutei").click()
        self.driver.find_element(By.ID, "BtnTsuika").click()
        self.driver.find_element(By.ID, "CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000012")
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.driver.find_element(By.ID, "span_Sel1").click()
        self.driver.find_element(By.ID, "span_CmdKShusei").click()
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").send_keys("令和05年12月02日")
        self.driver.find_element(By.ID, "span_CmdATsuika").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").send_keys("010003000")
        self.driver.find_element(By.ID, "TxtEndJushoCode").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("010003000")
        self.driver.find_element(By.ID, "CmdNKanryo").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF063_4",caption="汎用区域マスタ登録_※5の「登録」ボタン押下")

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "BtnKakutei").click()
        self.driver.find_element(By.ID, "BtnTsuika").click()
        self.driver.find_element(By.ID, "span_CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000012")
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.driver.find_element(By.ID, "span_Sel1").click()
        self.driver.find_element(By.ID, "span_CmdKShusei").click()
        self.driver.find_element(By.ID, "span_CmdATsuika").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").send_keys("1")
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("2")
        self.driver.find_element(By.ID, "span_CmdNKanryo").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.driver.find_element(By.ID, "span_ASel2").click()
        self.driver.find_element(By.ID, "span_CmdASakujyo").click()
        assert self.driver.switch_to.alert.text == "削除します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF063_5",caption="汎用区域マスタ登録_※6の「登録」ボタン押下")

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "BtnKakutei").click()
        self.driver.find_element(By.ID, "span_BtnTsuika").click()
        self.driver.find_element(By.ID, "span_CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000012")
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.driver.find_element(By.ID, "Sel1").click()
        self.driver.find_element(By.ID, "span_CmdKShusei").click()
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").send_keys("令和05年11月01日")
        self.driver.find_element(By.ID, "span_CmdATsuika").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").click()
        self.driver.find_element(By.ID, "TxtStartJushoCode").send_keys("010004000")
        self.driver.find_element(By.ID, "TxtEndJushoCode").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("010004000")
        self.driver.find_element(By.ID, "span_CmdNKanryo").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF063_6",caption="汎用区域マスタ登録_※7の「登録」ボタン押下")

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "span_BtnKakutei").click()
        self.driver.find_element(By.ID, "span_BtnTsuika").click()
        self.driver.find_element(By.ID, "CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000013")
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.driver.find_element(By.ID, "Sel1").click()
        self.driver.find_element(By.ID, "CmdKKirikae").click()
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").click()
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").send_keys("令和05年12月06日")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF063_6",caption="汎用区域マスタ登録_※8の「登録」ボタン押下")

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "span_BtnKakutei").click()
        self.driver.find_element(By.ID, "span_BtnTsuika").click()
        self.driver.find_element(By.ID, "CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000013")
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.driver.find_element(By.ID, "Sel1").click()
        self.driver.find_element(By.ID, "CmdKSakujyo").click()
        assert self.driver.switch_to.alert.text == "削除します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF063_6",caption="汎用区域マスタ登録_※9の「住所削除」ボタン押下")

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.driver.find_element(By.ID, "span_CmdButton14").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '高齢者実態調査']").click()
        self.driver.find_element(By.ID, "CmbChiku").click()
        dropdown = self.driver.find_element(By.ID, "CmbChiku")
        dropdown.find_element(By.XPATH, "//option[. = '警察署']").click()
        self.driver.find_element(By.ID, "BtnKakutei").click()
        self.driver.find_element(By.ID, "BtnTsuika").click()
        self.driver.find_element(By.ID, "CmdKuikiMente").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '第一区']").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("令和05年12月08日")
        self.driver.find_element(By.ID, "TxtKuikiCode").click()
        self.driver.find_element(By.ID, "TxtKuikiCode").send_keys("0000000014")
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.screen_shot("QMDF063_9",caption="汎用区域マスタ登録_「検索」ボタン押下")
        self.driver.find_element(By.ID, "Sel1").click()
        self.driver.find_element(By.ID, "CmdKShusei").click()
        self.driver.find_element(By.ID, "TxtYukoKYmdStart").send_keys("令和05年12月01日")
        self.driver.find_element(By.ID, "span_CmdKShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.screen_shot("QMDF063_9",caption="汎用区域マスタ登録_「住所情報：初期表示」ボタン押下")
        self.driver.find_element(By.ID, "span_CmdATsuika").click()
        self.driver.find_element(By.ID, "CmdAreaSentaku").click()
        self.screen_shot("QMDF063_9",caption="汎用区域マスタ登録_「住所明細一覧：住所明細選択」ボタン押下")
        
        self.driver.find_element(By.ID, "ASel1").click()
        self.driver.find_element(By.ID, "span_CmdAShusei").click()
        self.driver.find_element(By.ID, "span_CmdStartJushoCode").click()
        self.screen_shot("QMDF063_15",caption="汎用区域マスタ登録_「開始住所コード」ボタン押下")
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_CmdEndJushoCode").click()
        self.screen_shot("QMDF063_15",caption="汎用区域マスタ登録_「終了住所コード」ボタン押下")
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "TxtEndJushoCode").send_keys("010004000")
        self.driver.find_element(By.ID, "span_CmdAShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.screen_shot("QMDF063_9",caption="汎用区域マスタ登録_「住所明細情報：初期表示」ボタン押下")

        self.driver.find_element(By.ID, "span_CmdKuikiSentaku").click()
        self.screen_shot("QMDF063_15",caption="汎用区域マスタ登録_「住所一覧：住所選択」ボタン押下")
        self.form_input_by_id(idstr="CmbShokanku", text="")
        self.driver.find_element(By.ID, "TxtKijunbi").click()
        self.driver.find_element(By.ID, "TxtKijunbi").send_keys("平成17年12月01日")
        self.driver.find_element(By.ID, "CmdKensaku").click()

        self.driver.find_element(By.ID, "CmdTsugi").click()
        self.screen_shot("QMDF063_16",caption="汎用区域マスタ登録_「次頁」ボタン押下")
        self.driver.find_element(By.ID, "CmdMae").click()
        self.screen_shot("QMDF063_17",caption="汎用区域マスタ登録_「前頁」ボタン押下")
        self.driver.find_element(By.ID, "PageCmb").click()
        dropdown = self.driver.find_element(By.ID, "PageCmb")
        dropdown.find_element(By.XPATH, "//option[. = '02']").click()
        self.driver.find_element(By.ID, "CmdIdobtn").click()
        self.screen_shot("QMDF063_17",caption="汎用区域マスタ登録_「へ移動」ボタン押下")
        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.screen_shot("QMDF063_19",caption="汎用区域マスタ登録_「初期表示」ボタン押下")