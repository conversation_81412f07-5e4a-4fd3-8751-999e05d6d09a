import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120206(FukushiSiteTestCaseBase):
    """TestQAC060_1120206"""

    def setUp(self):
        case_data = self.test_data["TestQAC060_1120206"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC060", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC060_1120206.sql", params=sql_params)
        super().setUp()

    # 転出、死亡の住記減異動が発生した対象者をバッチ処理にて抽出、資格喪失の資格履歴を一括で作成できることを確認する。
    def test_QAC060_1120206(self):
        """住記減異動確認"""

        case_data = self.test_data["TestQAC060_1120206"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「特別児童扶養手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC060")

        # 8 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_8")

        # 9 特別児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 特別児童扶養手当資格管理画面: 申請種別「認定請求」選択申請理由「新規」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu", ""))

        # 11 特別児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 特別児童扶養手当資格管理画面: 申請日「20230110」担当所管区「第一区」選択誓約有無「チェック」
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230110")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value="1")

        # 13 特別児童扶養手当資格管理画面: 「児童追加」ボタン押下
        self.click_button_by_label("児童追加")

        # 14 世帯員検索画面: 表示
        self.screen_shot("世帯員検索画面_14")

        # 15 世帯員検索画面: 世帯員一覧「3」ボタン押下
        self.click_button_by_label("3")

        # 16 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_16")

        # 17 支給対象児童入力画面: 続柄「長男」を選択児童該当日「20230201」児童該当事由「児童増（身体障害者手帳）」選択児童同居別居区分「同居」チェック児童総合障害等級「２級」選択福祉行政報告例用障害分類「外部障害」選択児童障害分類「上肢の機能障害」選択児童有期認定年月「20241031」児童障害等級「２級」選択児童診断書様式「身体障害者手帳」選択
        self.form_input_by_id(idstr="CmbZokugara", text="長男")
        self.form_input_by_id(idstr="TxtGaitouYMD", value="20230201")
        self.form_input_by_id(idstr="CmbGaitouJiyu", text="児童増（身障手帳）")
        self.form_input_by_id(idstr="DoukyoBekkyoRBtn1", value="1")
        self.form_input_by_id(idstr="CmbShougaiToukyu", text="２級")
        self.form_input_by_id(idstr="CmbShougaiBunrui", text="外部障害")
        self.form_input_by_id(idstr="CmbByomei1", text="上肢の機能障害")
        self.form_input_by_id(idstr="TxtYuukiNinteiYMD1", value="20241031")
        self.form_input_by_id(idstr="CmbJidoShougaiToukyu1", text="2級")
        self.form_input_by_id(idstr="CmbJidoShindanshoYoshiki1", text="身体障害者手帳")

        # 18 支給対象児童入力画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 19 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_19")

        # 20 特別児童扶養手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 21 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_21")

        # 22 福祉世帯情報画面: 1に対して本人から見た続柄「本人」選択受給者との関係「本人」該当日「20230201」
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230201")
        self.form_input_by_id(idstr="ChkFlg_2", value="1")

        # 23 福祉世帯情報画面: 2に対して本人から見た続柄「子」選択受給者との関係「対象児童」該当日「20230201」
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230201")

        # 24 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 25 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_25")

        # 26 特別児童扶養手当資格管理画面: 開始年月「202302」
        self.form_input_by_id(idstr="TxtKaitei", value="202302")

        # 27 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 28 特別児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 29 特別児童扶養手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別児童扶養手当資格管理画面_29")

        # 30 特別児童扶養手当資格管理画面: 「進達入力」ボタン押下
        can_shintatsu_button = self.click_button_by_label("市から県への進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("市から県への進達入力")

            # 31 特別児童扶養手当資格管理画面: 進達日「20230202」進達判定年月日「20230202」進達結果「該当」
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230202")

            # 32 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 33 特別児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 34 特別児童扶養手当資格管理画面: 表示
            # Assert: 「登録しました」のメッセージチェック
            self.assert_message_area("登録しました")
            self.screen_shot("特別児童扶養手当資格管理画面_34")

            #35 特別児童扶養手当資格管理画面: 進達判定年月日「20230202」進達結果「該当」
            self.click_button_by_label("市から県への進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230202")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="該当")

            # 36 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 37 特別児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 38 特別児童扶養手当資格管理画面: 表示
            # Assert: 「登録しました」のメッセージチェック
            self.assert_message_area("登録しました")
            self.screen_shot("特別児童扶養手当資格管理画面_38")

        # 39 特別児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 40 特別児童扶養手当資格管理画面: 判定日「20230202」判定結果「決定」：証書番号：1120206
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230202")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtShoushoBango", value="1120206")

        # 41 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 42 特別児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 43 特別児童扶養手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別児童扶養手当資格管理画面_43")

        # 44 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_44")

        # 41 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 45 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_45")

        # 46 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：月次処理処理分類：住記異動処理
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="住記異動処理")

        # 47 バッチ起動画面: 「住記異動者自動消滅_抽出」のNoボタン押下
        self.click_batch_job_button_by_label("住記異動者自動消滅_抽出")

        # 48 バッチ起動画面: 対象開始年月日「20230401」対象終了年月日「20230702」出力順「証書番号順」選択
        params = [
            {"title": "対象開始年月日", "type": "text", "value": "20230401"},
            {"title": "対象終了年月日", "type": "text", "value": "20230702"},
            {"title": "出力順", "type": "select", "value": "証書番号順"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_45")

        # 49 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 50 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 51 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_51")

        # 52 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 53 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_53")

        # 54 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 55 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_55")

        # 56 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 57 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_57")

        # 58 ジョブ帳票履歴画面: 「自動消滅一覧(消滅対象・エラーロスト含む)」のNoボタン押下
        # self.click_button_by_label("自動消滅一覧(消滅対象・エラーロスト含む)")

        # 59 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 60 自動消滅一覧(消滅対象・エラーロスト含む)（PDF）: 表示
        # self.screen_shot("自動消滅一覧(消滅対象・エラーロスト含む)（PDF）_57")

        # 61 自動消滅一覧(消滅対象・エラーロスト含む)（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("自動消滅一覧(消滅対象・エラーロスト含む)（PDF）_58")

        # 62 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 63 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 64 バッチ起動画面: 「住記異動者自動消滅_更新」のNoボタン押下
        self.click_batch_job_button_by_label("住記異動者自動消滅_更新")

        # 65 バッチ起動画面: 決定年月日「20230702」
        params = [
            {"title": "決定年月日", "type": "text", "value": "20230702"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_65")

        # 66 バッチ起動画面: 「処理開始」ボタン押下
        self.exec_batch_job()

        # 67 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 68 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_68")

        # 69 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 70 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_70")

        # 71 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_71")

        # 72 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 73 個人検索画面: 表示
        self.screen_shot("個人検索画面_73")

        # 74 個人検索画面: 「住民コード」入力

        # 75 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 76 受給状況画面: 表示
        self.screen_shot("受給状況画面_76")

        # 77 受給状況画面: 「特別児童扶養手当」ボタン押下
        self.click_button_by_label("特別児童扶養手当")

        # 78 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_78")
