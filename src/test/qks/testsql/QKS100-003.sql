use WR$$JICHITAI_CODE$$QA

update QKS貸付資格内容
set 貸付決定金額 = 当初貸付決定金額
,	据置開始年月 = '202504'
from QKS貸付資格内容
where 業務コード = 'QKS100'
	AND 貸付番号 = '1000000336'
	AND 削除フラグ = '0'

update QKS交付履歴基本
set 削除フラグ = '1'
from QKS交付履歴基本
where 業務コード = 'QKS100'
	AND 貸付番号 = '1000000336'
	AND 申請種別 <> 1
	AND 削除フラグ = '0'

update QKS交付履歴詳細
set 削除フラグ = '1'
from QKS交付履歴詳細
where 業務コード = 'QKS100'
	AND 貸付番号 = '1000000336'
	AND 削除フラグ = '0'
	AND NOT EXISTS
	(
		select *
		from QKS交付履歴基本
		where 業務コード = 'QKS100'
			AND 貸付番号 = '1000000336'
			AND 申請種別 = 1
			AND 削除フラグ = '0'
			AND QKS交付履歴基本.交付履歴番号 = QKS交付履歴詳細.交付履歴番号
	)


DELETE FROM QKS資格履歴 WHERE 業務コード = 'QKS100' and 宛名コード = '$$QKS100_ATENACODE003$$' and 履歴番号 = 483
DELETE FROM QKS交付履歴詳細 WHERE 業務コード = 'QKS100' and 貸付番号 = '1000000336' and 交付履歴番号 = 483

insert into QKS資格履歴 values ('QKS100',483,81,'99101','00000','','$$QKS100_ATENACODE003$$','20131127',1,0,'00000000','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20131127',2,0,'00000000','0000000064','','','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSF004 ');

insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202104',0,'99301','01','202104','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202105',0,'99301','01','202104','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202106',0,'99301','01','202104','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202107',0,'99301','01','202104','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202108',0,'99301','01','202104','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202109',0,'99301','01','202104','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202110',0,'99301','01','202110','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202111',0,'99301','01','202110','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202112',0,'99301','01','202110','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202201',0,'99301','01','202110','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202202',0,'99301','01','202110','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202203',0,'99301','01','202110','2021',50000,0,50000,'00000000','0000','20210401',0,'00000000','00000000','0','0','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202204',0,'99301','01','202204','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202205',0,'99301','01','202204','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202206',0,'99301','01','202204','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202207',0,'99301','01','202204','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202208',0,'99301','01','202204','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202209',0,'99301','01','202204','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202210',0,'99301','01','202210','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202211',0,'99301','01','202210','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202212',0,'99301','01','202210','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202301',0,'99301','01','202210','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202302',0,'99301','01','202210','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202303',0,'99301','01','202210','2022',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202304',0,'99301','01','202304','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202305',0,'99301','01','202304','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202306',0,'99301','01','202304','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202307',0,'99301','01','202304','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202308',0,'99301','01','202304','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202309',0,'99301','01','202304','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202310',0,'99301','01','202310','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202311',0,'99301','01','202310','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202312',0,'99301','01','202310','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202401',0,'99301','01','202310','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202402',0,'99301','01','202310','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202403',0,'99301','01','202310','2023',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202404',0,'99301','01','202404','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202405',0,'99301','01','202404','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202406',0,'99301','01','202404','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202407',0,'99301','01','202404','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202408',0,'99301','01','202404','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202409',0,'99301','01','202404','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202410',0,'99301','01','202410','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202411',0,'99301','01','202410','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202412',0,'99301','01','202410','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202501',0,'99301','01','202410','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202502',0,'99301','01','202410','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','0','QKS100-003','QKS100-003',getdate(),getdate(),'QKSD003 ');
insert into QKS交付履歴詳細 values ('99101','QKS100','1000000336',483,'202503',0,'99301','01','202410','2024',50000,0,50000,'00000000','0000','00000000',0,'00000000','00000000','0','1','00000000','00000000','0000','000',N'','',N'','','0','0','',N'',N'',N'','0','0','1','QKS100-003','QKS100-003',getdate(),convert(DateTime,'2023-12-26 19:18:25.463',21),'QKSD003 ');