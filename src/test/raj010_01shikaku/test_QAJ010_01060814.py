import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060814(FukushiSiteTestCaseBase):
    """TestQAJ010_01060814"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060814"]
        super().setUp()
    
    # 障害福祉サービス受給者証を出力できることを確認する。
    def test_QAJ010_01060814(self):
        """障害福祉サービス受給者証出力"""
        
        case_data = self.test_data["TestQAJ010_01060814"]
        atena_code = case_data.get("atena_code", "")
        hakkoTxt = case_data.get("hakkoTxt", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス申請管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        
        # 3 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_3")
        
        # 4 帳票印刷画面: 「障害福祉サービス受給者証」行の印刷チェックボックス選択、「障害福祉サービス受給者証」行の発行年月日チェックボックス選択、発行年月日「20230601」入力
        # 5 帳票印刷画面: 「印刷」ボタン押下
        exec_params = [
            {"report_name": "障害福祉サービス受給者証（A4_1）",
            "params":[
                    {"title": "交付日", "value": hakkoTxt}
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_4")
        
        # 6 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        
        # 7 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_7")
        
        # 8 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 9 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        
        # 10 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_10")
        
