import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01020408(FukushiSiteTestCaseBase):
    """TESTRAA01020408"""
    
    def setUp(self):
        super().setUp()
    
    def test_case_raa010204_08(self):
        """test_case_raa010204_08"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        hakkou_date = case_data.get("hakkou_date", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA010")
        # 1 身体障害者手帳資格管理画面:「印刷」ボタン押下
        self.click_button_by_label("印刷")
        # 2 帳票印刷画面:表示
        self.save_screenshot_migrate(driver, "raa010204_08-02" , True)
        
        # 3 帳票印刷画面:「身体障害者手帳居住地等変更通知書」行の印刷チェックボックス選択
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name,
                "params": [
                    {"title": "発行年月日", "type": "text", "value": hakkou_date}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        
        # 5 帳票印刷画面:「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")

        # 6 帳票印刷画面: 帳票（PDF）表示
        self.save_screenshot_migrate(driver, "raa010204_08-06" , True)

        # 7 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 8  帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 9 帳票印刷画面:表示
        self.save_screenshot_migrate(driver, "raa010204_08-09" , True)
