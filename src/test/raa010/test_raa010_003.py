import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA010003(FukushiSiteTestCaseBase):
    """TESTRAA010003"""
    
    def test_case_raa010_003(self):
        """test_case_raa010_003"""
        driver = None
        test_data = self.common_test_data
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("case_common_raa010_atena_code1"), gyoumu_code="QAA010")
        self.save_screenshot_migrate(driver, "RAA003-001-5-2", True)

        if self.exist_hanntei_info() and self.exist_shintatsu_info():
        # 判定結果画面利用有無と進達有無がいずれも「有」の場合
            self.find_element(By.ID,"CmdShuusei").click()
            self.find_common_buttons()
            self.common_button_click(button_text="判定情報")
            self.save_screenshot_migrate(driver, "RAA003-001-5-5", True)

            self.find_element(By.ID,"CmdShinsaKekka").click()
            self.find_element(By.ID,"CmdSyogaiTuika").click()
            self.save_screenshot_migrate(driver, "RAA003-001-5-8", True)

            #障害内容入力
            self.find_element(By.ID,"TxtNinteiYMD").clear()
            self.find_element(By.ID,"TxtNinteiYMD").send_keys("20210807")
            self.find_element(By.ID,"CmbShogaiKubun_1").send_keys("視覚")
            self.find_element(By.ID,"CmbTokyu_1").send_keys("１級")
            self.find_element(By.ID,"CmbHobetsu_1").send_keys("１－１")
            self.find_element(By.ID,"TxtYukiNinteiYMD_1").clear()
            self.find_element(By.ID,"TxtYukiNinteiYMD_1").send_keys("202501")
            self.find_element(By.ID,"CmbGeninKubun_1").click()
            self.select_Option(driver,self.find_element(By.ID,"CmbGeninKubun_1"),"原因")
            self.find_element(By.ID,"CmdKensaku_1").click()
            self.find_element(By.ID,"CmbGeninJokyo_1").click()
            self.select_Option(driver,self.find_element(By.ID,"CmbGeninJokyo_1"),"緑内障")
            self.find_element(By.ID,"CmbShushokugo_1").send_keys("による")
            
            self.find_element(By.ID,"CmbGeninKubun_2").click()
            self.select_Option(driver,self.find_element(By.ID,"CmbGeninKubun_2"),"内容")
            self.find_element(By.ID,"CmdKensaku_2").click()
            self.find_element(By.ID,"CmbGeninJokyo_2").click()
            self.select_Option(driver,self.find_element(By.ID,"CmbGeninJokyo_2"),"両眼の視力の和が0.01以下のもの")
            self.find_element(By.ID,"CmdGeninHanei").click()
            self.save_screenshot_migrate(driver, "RAA003-001-5-18", True)

            self.find_element(By.ID,"CmdNyuryoku").click()
            self.save_screenshot_migrate(driver, "RAA003-001-5-20", True)

            self.find_element(By.XPATH,"//span[@id='CmdSougouToukyu']").click()
            self.find_element(By.XPATH,"//span[@id='CmdShubetsu']").click()
            self.find_element(By.ID,"TechoBangoCmb").click()
            self.select_Option(driver,self.find_element(By.ID,"TechoBangoCmb"),"神奈川県")
            self.find_element(By.ID,"TxtTechoBango").clear()
            self.find_element(By.ID,"TxtTechoBango").send_keys("003010004")
            self.find_element(By.ID,"TxtkoufuYMD").clear()
            self.find_element(By.ID,"TxtkoufuYMD").send_keys("20210807")
            
            #登録
            self.find_element(By.ID,"span_CmdTouroku").click()
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.save_screenshot_migrate(driver, "RAA003-001-5-26", True)

            self.find_element(By.ID,"span_CmdShinsaKessai").click()
            self.find_element(By.ID,"KessaiJoutaiCmb").click()
            self.select_Option(driver,self.find_element(By.ID,"KessaiJoutaiCmb"),"確定")
            
            self.find_element(By.ID,"CmdTouroku").click()
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.save_screenshot_migrate(driver, "RAA003-001-5-29", True)

            self.return_click()
            self.save_screenshot_migrate(driver, "RAA003-001-5-31", True)

            self.find_element(By.ID,"CmdTouroku").click()
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

            self.return_click()
            self.return_click()