import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class Test_QAZF022(FukushiSiteTestCaseBase):
    """Test_QAZF022"""

    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAZF022_実行前スクリプト.sql", params=atena_list)
        super().setUp()

    def tearDown(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAZF022_実行後スクリプト.sql", params=atena_list)
        super().tearDown()

    def test_case_001(self):
        """test_case_001"""  

        test_data = self.common_test_data        
        self.do_login()
        self.click_button_by_label("申請資格管理")
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.driver.find_element(By.ID, "AtenaCD").send_keys(test_data.get("qazf022_atena_code"))
        self.driver.find_element(By.ID, "Kensaku").click()
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="所得情報(共通税)")
        self.driver.find_element(By.ID, "CmbGyoumu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyoumu")
        dropdown.find_element(By.XPATH, "//option[. = '障害']").click()
        self.driver.find_element(By.ID, "CmbJigyou").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyou")
        dropdown.find_element(By.XPATH, "//option[. = '補装具費支給']").click()   
        self.driver.find_element(By.ID, "CmdSearch").click()             
        self.driver.find_element(By.ID, "span_CmdDoui").click()
        self.screen_shot("QAZF022_1",caption="所得参照同意情報_初期表示")
        self.driver.find_element(By.ID, "span_ALLSelect").click()
        assert self.driver.switch_to.alert.text == "全員同意有りに変更します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "TbxDouiStartYMD0").click()
        self.driver.find_element(By.ID, "TbxDouiStartYMD0").send_keys("令和05年12月05日")
        self.driver.find_element(By.ID, "TbxDouiStartYMD1").click()
        self.driver.find_element(By.ID, "TbxDouiStartYMD1").send_keys("令和05年12月05日")
        self.driver.find_element(By.ID, "TbxDouiStartYMD2").click()
        self.driver.find_element(By.ID, "TbxDouiStartYMD2").send_keys("令和05年12月05日")
        self.driver.find_element(By.ID, "TbxDouiEndYMD0").click()
        self.driver.find_element(By.ID, "TbxDouiEndYMD0").send_keys("令和06年01月31日")
        self.driver.find_element(By.ID, "TbxDouiEndYMD1").click()
        self.driver.find_element(By.ID, "TbxDouiEndYMD1").send_keys("令和06年01月31日")
        self.driver.find_element(By.ID, "TbxDouiEndYMD2").click()
        self.driver.find_element(By.ID, "TbxDouiEndYMD2").send_keys("令和06年01月31日")

        self.driver.find_element(By.ID, "span_CmdRegist").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF022_2",caption="所得参照同意情報_※1「登録」ボタン押下")


        self.driver.find_element(By.ID, "CmdDoui").click()
        self.driver.find_element(By.ID, "span_ALLCancel").click()
        assert self.driver.switch_to.alert.text == "全員同意無しに変更します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "span_CmdRegist").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF022_3",caption="所得参照同意情報_※2「登録」ボタン押下")
        

        self.driver.find_element(By.ID, "CmdDoui").click()
        self.driver.find_element(By.ID, "ALLSelect").click()
        assert self.driver.switch_to.alert.text == "全員同意有りに変更します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF022_4",caption="所得参照同意情報_「全員同意有り」ボタン押下")
        self.driver.find_element(By.ID, "span_ALLCancel").click()
        assert self.driver.switch_to.alert.text == "全員同意無しに変更します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF022_5",caption="所得参照同意情報_「全員同意無し」ボタン押下")
        self.driver.find_element(By.ID, "span_ALLSelect").click()
        assert self.driver.switch_to.alert.text == "全員同意有りに変更します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.driver.find_element(By.ID, "span_CmdDefault").click()
        self.screen_shot("QAZF022_6",caption="所得参照同意情報_「初期表示」ボタン押下")