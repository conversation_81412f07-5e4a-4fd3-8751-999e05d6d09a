import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

class TestQAZF049(FukushiSiteTestCaseBase):
  """Test_QAZF049"""

    # 各テストメソッドの実行前に実行したいもの   
  def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAZF049_実行前スクリプト.sql", params=atena_list) 
        super().setUp()  

  def tearDown(self):
        self.exec_sqlfile("QAZF049_実行後スクリプト.sql")
        super().tearDown()

  def test_case_001(self):
    """test_case_001"""
    
    driver = None       
    test_data = self.common_test_data        
    self.do_login()
    self.click_button_by_label("通知書管理")
    self.driver.find_element(By.ID, "CmbGyomu").click()
    dropdown = self.driver.find_element(By.ID, "CmbGyomu")
    dropdown.find_element(By.XPATH, "//option[. = '障害']").click()
    self.driver.find_element(By.ID, "CmbJigyo").click()
    dropdown = self.driver.find_element(By.ID, "CmbJigyo")
    dropdown.find_element(By.XPATH, "//option[. = '身体障害者手帳']").click()
    self.driver.find_element(By.ID, "span_CmdKakutei").click()
    self.driver.find_element(By.ID, "CmbChohyo").click()
    dropdown = self.driver.find_element(By.ID, "CmbChohyo")
    dropdown.find_element(By.XPATH, "//option[. = '身体障害者手帳交付証明書']").click()
    # self.driver.find_element(By.ID, "CmbNendo").click()
    # dropdown = self.driver.find_element(By.ID, "CmbNendo")
    # dropdown.find_element(By.XPATH, "//option[. = '令和05年']").click()
    self.driver.find_element(By.ID, "CmdKensaku").click()
    self.save_screenshot_migrate(driver, "QAZF049-11" , True)       
    self.driver.find_element(By.ID, "ChkShori_1").click()
    self.driver.find_element(By.ID, "CmdShoki").click()
    self.save_screenshot_migrate(driver, "QAZF049-13" , True)        
    self.driver.find_element(By.ID, "CmdAllSelect").click()
    assert self.driver.switch_to.alert.text == "全件選択します。よろしいですか？"
    self.driver.switch_to.alert.accept()
    self.save_screenshot_migrate(driver, "QAZF049-14" , True)        
    self.driver.find_element(By.ID, "span_CmdAllCancel").click()
    assert self.driver.switch_to.alert.text == "全件解除します。よろしいですか？"
    self.driver.switch_to.alert.accept()
    self.save_screenshot_migrate(driver, "QAZF049-15" , True)            
    self.driver.find_element(By.ID, "SelBtn_1").click()

    self.save_screenshot_migrate(driver, "QAZF049-17" , True)           
    self.driver.find_element(By.ID, "GOBACK").click()
    self.driver.find_element(By.ID, "ChkShori_1").click()
    self.save_screenshot_migrate(driver, "QAZF049-19" , True)   
    self.driver.find_element(By.ID, "CmdTouroku").click()
    assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
    self.driver.switch_to.alert.accept()
    self.save_screenshot_migrate(driver, "QAZF049-21" , True)         
    self.driver.find_element(By.ID, "LineCmb").click()
    dropdown = self.driver.find_element(By.ID, "LineCmb")
    dropdown.find_element(By.XPATH, "//option[. = '11－13']").click()
    self.driver.find_element(By.ID, "CmdJumpLine").click()
    self.save_screenshot_migrate(driver, "QAZF049-23" , True)        
    self.driver.find_element(By.ID, "CmdSortShimei").click()
    self.save_screenshot_migrate(driver, "QAZF049-24" , True)            
    self.driver.find_element(By.ID, "CmdSortShimei").click()
    self.save_screenshot_migrate(driver, "QAZF049-25" , True)      
 
           