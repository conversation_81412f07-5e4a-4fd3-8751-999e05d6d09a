import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QAZF010(FukushiSiteTestCaseBase):
    """Test_QAZF010"""

    def test_case_001(self):
        """test_case_001"""
        driver = None
        test_data = self.common_test_data
        
        self.do_login()
        self.click_button_by_label("申請書消込")
        self.save_screenshot_migrate(driver, "QAZF010-01-「申請書消込」ボタン押下" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        self.find_element(By.ID,"CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAZF010-02-「初期表示」ボタン押下" , True)

        self.find_element(By.ID,"JigyoCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"JigyoCmb"),"障害者総合支援")
        self.find_element(By.ID,"TxtkaiseiJisibi").click()
        self.find_element(By.ID,"TxtkaiseiJisibi").send_keys("")
        self.find_element(By.ID,"TxtkaiseiJisibi").send_keys("20210901")
        self.find_element(By.ID,"Txtteisyutubi").click()
        self.find_element(By.ID,"Txtteisyutubi").send_keys("")
        self.find_element(By.ID,"Txtteisyutubi").send_keys("20210901")
        self.find_element(By.ID,"TxtAtenaCode").click()
        self.find_element(By.ID,"TxtAtenaCode").send_keys("")
        self.find_element(By.ID,"TxtAtenaCode").send_keys(test_data.get("qazf010_atena_code"))
        self.find_element(By.ID,"span_CmdKensaku").click()
        self.find_element(By.ID,"span_BtnTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.save_screenshot_migrate(driver, "QAZF010-03-「検索」ボタン押下" , True)

        self.find_element(By.ID,"Sel1").click()
        self.save_screenshot_migrate(driver, "QAZF010-04-No.1選択" , True)
        self.find_element(By.ID,"btnCommon2").click()
        self.save_screenshot_migrate(driver, "QAZF010-05-「所得情報」ボタン押下" , True)

        self.find_element(By.ID,"span_CmdSearch").click()
        self.save_screenshot_migrate(driver, "QAZF010-06-検索ボタン" , True)

        self.find_element(By.ID,"span_CmdDefault").click()
        self.save_screenshot_migrate(driver, "QAZF010-07-初期表示ボタン" , True)

        self.find_element(By.ID,"span_CmdNo0").click()
        self.save_screenshot_migrate(driver, "QAZF010-08-No1ボタン" , True)
        self.find_element(By.ID,"GOBACK").click()

        self.find_element(By.ID,"span_CmdInsatsu").click()
        assert self.driver.switch_to.alert.text == "印刷します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.save_screenshot_migrate(driver, "QAZF010-09-印刷ボタン" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"btnCommon3").click()
        self.save_screenshot_migrate(driver, "QAZF010-10-所得情報（参照）ボタン" , True)
