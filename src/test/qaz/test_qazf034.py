import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class Test_QAZF034(FukushiSiteTestCaseBase):
    """Test_QAZF034"""

    def test_case_001(self):
        """test_case_001"""
        driver = None
        self.do_login()
        self.click_button_by_label("進達処理")
        self.driver.find_element(By.ID, "Gyomu").click()
        dropdown = self.driver.find_element(By.ID, "Gyomu")
        dropdown.find_element(By.XPATH, "//option[. = '障害']").click()
        self.driver.find_element(By.ID, "Jigyo").click()
        dropdown = self.driver.find_element(By.ID, "Jigyo")
        dropdown.find_element(By.XPATH, "//option[. = '補装具費支給']").click()
        self.driver.find_element(By.ID, "KakuteiBtn").click()
        self.driver.find_element(By.ID, "ShinseiKaishiYMD").click()
        self.driver.find_element(By.ID, "ShinseiKaishiYMD").send_keys("平成23年04月22日")
        self.driver.find_element(By.ID, "ShinseiOwariYMD").click()
        self.driver.find_element(By.ID, "ShinseiOwariYMD").send_keys("平成23年04月22日")
        self.driver.find_element(By.ID, "KensakuBtn").click()
        self.driver.find_element(By.ID, "NoBtn1").click()
        self.find_common_buttons()
        self.common_button_click(button_text="メモ情報")
        self.screen_shot("QAZF034_1",caption="QAZF034_初期表示") 

        self.driver.find_element(By.ID, "CmdTsuika").click()
        self.driver.find_element(By.ID, "CmdNyurkTanto").click()
        self.screen_shot("QAZF034_2",caption="QAZF034_「入力担当者」ボタン押下") 
            
        self.driver.find_element(By.ID, "CmdToroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF034_3",caption="QAZF034_メモ情報追加") 

        self.driver.find_element(By.ID, "span_CmdShusei").click()
        self.driver.find_element(By.ID, "TxtNyuryokuHH").click()
        self.driver.find_element(By.ID, "TxtNyuryokuHH").send_keys("16")
        self.driver.find_element(By.ID, "CmdToroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF034_4",caption="QAZF034_メモ情報更新") 

        self.driver.find_element(By.ID, "span_CmdShusei").click()
        self.driver.find_element(By.ID, "TxtNyuryokuHH").click()
        self.driver.find_element(By.ID, "TxtNyuryokuHH").send_keys("17")
        self.driver.find_element(By.ID, "span_CmdTorikeshi").click()
        self.screen_shot("QAZF034_5",caption="QAZF034_処理取消") 
        
        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.screen_shot("QAZF034_6",caption="QAZF034_「初期表示」ボタン押下") 

        self.driver.find_element(By.ID, "span_CmdSakujo").click()
        assert self.driver.switch_to.alert.text == "削除します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF034_7",caption="QAZF034_メモ情報削除") 

        self.driver.find_element(By.ID, "Sel1").click()
        self.screen_shot("QAZF034_8",caption="QAZF034_「メモ情報履歴のNO1データ」押下") 

        self.driver.find_element(By.ID, "CmdSortNyurkNichiji").click()
        self.driver.find_element(By.ID, "CmdSortNyurkNichiji").click()
        self.screen_shot("QAZF034_9",caption="QAZF034_「入力日時：△」ボタン押下")         
        
        self.driver.find_element(By.ID, "CmdSortNyurkNichiji").click()
        self.screen_shot("QAZF034_10",caption="QAZF034_「入力日時：▽」ボタン押下") 

        self.driver.find_element(By.ID, "PageCmb").click()
        dropdown = self.driver.find_element(By.ID, "PageCmb")
        dropdown.find_element(By.XPATH, "//option[. = '02']").click()
        self.driver.find_element(By.ID, "CmdJumpPage").click()
        self.screen_shot("QAZF034_11",caption="QAZF034_「へ移動」ボタン押下")         
        
        self.driver.find_element(By.ID, "CmdBackPage").click()
        self.screen_shot("QAZF034_12",caption="QAZF034_「前頁」ボタン押下")                 
        
        self.driver.find_element(By.ID, "CmdNextPage").click()
        self.screen_shot("QAZF034_13",caption="QAZF034_「次頁」ボタン押下") 