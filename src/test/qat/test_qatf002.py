import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QATF002(FukushiSiteTestCaseBase):
    """Test_QATF002"""

    def test_case_001(self):
        """test_case_001"""
        driver = None
        self.do_login()
        self.click_button_by_label("交付券消込")
        self.save_screenshot_migrate(driver, "QATF002_01_画面初期化" , True)

        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '障害']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '汎用帳票テストT']").click()
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QATF002_02_検索ボタン押下" , True)

        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "Radio1_1").click()
        self.driver.find_element(By.ID, "CmbNendo").click()
        dropdown = self.driver.find_element(By.ID, "CmbNendo")
        dropdown.find_element(By.XPATH, "//option[. = '平成22年']").click()
        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '全区']").click()        
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QATF002_03_検索ボタン押下" , True)
        self.driver.find_element(By.ID, "GOBACK").click()
        self.save_screenshot_migrate(driver, "QATF002_04_戻るボタン押下" , True)      
        self.driver.find_element(By.ID, "CmdShokiHyoji").click()     
        self.save_screenshot_migrate(driver, "QATF002_05_初期表示" , True)        
