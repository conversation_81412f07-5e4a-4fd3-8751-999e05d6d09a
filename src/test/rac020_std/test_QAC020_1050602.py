import time
from base.fukushi_case import FukushiSiteTestCaseBase

class TestQAC020_1050602(FukushiSiteTestCaseBase):
    """TestQAC020_1050602"""

    def setUp(self):
        super().setUp()

    # 債権情報の登録できることを確認する。
    def test_QAC020_1050602(self):
        case_data = self.test_data["TestQAC020_1050602"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu_cmb = case_data.get("shinsei_shubetsu_cmb", "")
        shinsei_riyuu_cmb = case_data.get("shinsei_riyuu_cmb", "")
        txt_shinsei_ymd = case_data.get("txt_shinsei_ymd", "")
        txt_kaitei = case_data.get("txt_kaitei", "")
        txt_nintei_ymd1 = case_data.get("txt_nintei_ymd1", "")
        hanyo1_sel=case_data.get("hanyo1_sel", "")
        shougai1_cmb=case_data.get("shougai1_cmb", "")
        shintasu_btn = case_data.get("shintasu_btn", "")
        shintatsu_ymd = case_data.get("shintatsu_ymd", "")
        shintatsu_hantei_ymd = case_data.get("shintatsu_hantei_ymd", "")
        shintasu_hantei = case_data.get("shintasu_hantei", "")
        txt_kettei_ymd = case_data.get("txt_kettei_ymd", "")
        kettei_kekka_cmb = case_data.get("kettei_kekka_cmb", "")

         # 1 メインメニュー画面: 表示
        self.do_login()
        # 39 メインメニュー画面: メインメニューから「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()
        self.screen_shot("個人検索表示用画面_39")

        # 40 個人検索表示用画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索表示用画面_40")

        # 41 個人検索表示用画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("福祉制度受給状況参照画面_41")

        # 42 受給状況画面: 「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("障害児福祉手当資格管理画面_42")

        # 43 障害児福祉手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 44 障害児福祉手当資格管理画面: 申請種別「変更」選択 申請理由「有期更新」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu_cmb)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu_cmb)
        self.screen_shot("障害児福祉手当資格管理画面_44")

        # 45 障害児福祉手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 46 障害児福祉手当資格管理画面: "申請日「20230601」 改定年月「202306」有期認定年月１「202510」"
        # 申請日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=txt_shinsei_ymd)

        # 改定年月
        self.form_input_by_id(idstr="TxtKaitei", value=txt_kaitei)

        # 有期認定年月１
        self.form_input_by_id(idstr="TxtNinteiYMD1", value=txt_nintei_ymd1)

        # 認定基準１
        self.form_input_by_id(idstr="SelectHanyo1", text=hanyo1_sel)
        
        # 障害区分１
        self.form_input_by_id(idstr="Shougai1Cmb", text=shougai1_cmb)
        self.screen_shot("障害児福祉手当資格管理画面_46")

        # 47 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")
        time.sleep(1)

        # 48 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(1)
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_48")

        # 49 障害児福祉手当資格管理画面: 「進達入力」ボタン押下
        # 50 障害児福祉手当資格管理画面: "進達日「20230601」 進達判定年月日「20230601」 進達結果「該当」選択"
        # 51 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        # 52 障害児福祉手当資格管理画面: 「登録」ボタン押下
        can_shintatsu_button = self.click_button_by_label(shintasu_btn)
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsuYMD", value=shintatsu_ymd)
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=shintatsu_hantei_ymd)
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text=shintasu_hantei)
            self.screen_shot("障害児福祉手当資格管理画面_50")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました。")
            self.screen_shot("障害児福祉手当資格管理画面_52")
        
        # 53 障害児福祉手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 54 障害児福祉手当資格管理画面: "判定日「20230601」
        #   判定結果「決定」選択"
        # 判定日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=txt_kettei_ymd)

        # 判定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=kettei_kekka_cmb)
        self.screen_shot("障害児福祉手当資格管理画面_54")

        # self.click_button_by_label("福祉世帯情報")
        # self.click_by_id(idstr="ChkFlg_2")
        # self.click_button_by_label("入力完了")

        # 55 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 56 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_56")