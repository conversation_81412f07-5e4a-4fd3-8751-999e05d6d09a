from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC010_1050603(FukushiSiteTestCaseBase):
    """TestQAC010_1050603"""

    def setUp(self):
        super().setUp()

    # 有期認定更新者に対し、再認定通知書が出力できることを確認する。
    def test_QAC010_1050603(self):
        """有期認定更新者への各種帳票作成"""

        case_data = self.test_data["TestQAC010_1050603"]
        gyomu_select = case_data.get("gyomu_select", "")
        jigyo_select = case_data.get("jigyo_select", "")
        shori_kubun_select = case_data.get("shori_kubun_select", "")
        shori_bunrui_select = case_data.get("shori_bunrui_select", "")
        hakko_ymd = case_data.get("hakko_ymd", "")
        start_ymd = case_data.get("start_ymd", "")
        end_ymd = case_data.get("end_ymd", "")
        ouput_order = case_data.get("ouput_order", "")
        batch_job_001 = case_data.get("batch_job_001", "")

        self.do_login()

        # 58 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()
        self.screen_shot("バッチ起動画面_58")

        # 59 バッチ起動画面: 業務：障害 事業：特別障害者手当 処理区分：月次処理 処理分類：通知書出力
        self.form_input_by_id(idstr="GyomuSelect", text=gyomu_select)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyo_select)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shori_kubun_select)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shori_bunrui_select)

        # 60 バッチ起動画面: 「再認定通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label(batch_job_001)

        # 61 バッチ起動画面: 発行年月日「20230501」抽出開始年月日「20230601」抽出終了年月日「20230630」出力順「認定番号順」選択
        params = [
            {"title": "発行年月日", "type": "text", "value": hakko_ymd},
            {"title": "抽出開始年月日", "type": "text", "value": start_ymd},
            {"title": "抽出終了年月日", "type": "text", "value": end_ymd},
            {"title": "出力順", "type": "select", "value": ouput_order}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_61")

        # 62 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.screen_shot("バッチ起動画面_62")

        # 63 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_63")

        # 64 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120, 20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_64")

        # 65 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("ジョブ帳票履歴画面_65")

        # 66 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ帳票履歴画面_66")

        # 67 ジョブ帳票履歴画面: 「障害児福祉手当再認定通知書」のNoボタン押下

        # 68 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 69 障害児福祉手当資格管理画面（PDF）: 障害児福祉手当再認定通知書を×ボタン押下でPDFを閉じる

