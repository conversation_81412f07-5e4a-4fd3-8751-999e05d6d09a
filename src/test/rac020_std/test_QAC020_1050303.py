from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050303(FukushiSiteTestCaseBase):
    """TestQAC020_1050303"""

    def setUp(self):
        #case_data = self.common_test_data.get(self.__class__.__name__, {})
        case_data = self.test_data["TestQAC020_1050303"]
        super().setUp()

    # 資格喪失届を提出した住民に対し決定登録ができることを確認する。
    def test_QAC020_1050303(self):
        """資格喪失処理"""

        #case_data = self.common_test_data.get(self.__class__.__name__, {})
        case_data = self.test_data["TestQAC020_1050303"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.do_login()

        # 25 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 26 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索画面_26")

        # 27 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 28 受給状況画面: 「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("受給者台帳画面_28")

        # 29 障害児福祉手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 30 "帳票印刷画面": 「障害状態再審査（診断）請求書」行の印刷チェックボックス選択
        exec_params = [
            {
                "report_name": report_name,
                "params": [
                    {"title": "発行年月日", "value": hakkou_ymd, "is_no_print": "1"}
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_30")
        self.assert_message_area("プレビューを表示しました")

        # 31 帳票印刷画面: 「印刷」ボタン押下

        # 32 帳票印刷画面: 障害状態再審査（診断）請求書「ファイルを開く(O)」ボタンを押下

        # 33 障害児福祉手当認定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 34 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

