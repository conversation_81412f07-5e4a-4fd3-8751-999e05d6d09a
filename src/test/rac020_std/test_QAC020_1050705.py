from base.fukushi_case import FukushiSiteTestCaseBase



class TestQAC020_1050705(FukushiSiteTestCaseBase):
    """TestQAC020_1050705"""

    def setUp(self):
        super().setUp()

    # 不支給情報を登録し、支払予定が変更されていることを確認する。
    def test_QAC020_1050705(self):

        case_data = self.test_data["TestQAC020_1050705"]
        atena_code = case_data.get("atena_code", "")
        riyu = case_data.get("riyu", "")
        start_ymd = case_data.get("start_ymd", "")
        end_ymd = case_data.get("end_ymd", "")
        kettei_ymd = case_data.get("kettei_ymd", "")


        # 114 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_114")

        # 115 メインメニュー画面: メインメニューから「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()
        self.screen_shot("個人検索表示用画面_115")

        # 116 個人検索画面:「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索表示用画面_116")
        
        # 117 個人検索画面:「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("福祉制度受給状況参照画面_117")

        # 118 福祉制度受給状況参照画面:「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("障害児福祉手当資格管理画面_118")
        
        # 119 障害児福祉手当資格管理画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        # 120 障害児福祉手当資格管理画面:「不支給情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="不支給情報")
        self.screen_shot("不支給情報画面_120")

        # 121 不支給情報画面:「追加」ボタン押下
        self.click_button_by_label("追加")
        self.screen_shot("不支給情報画面_121")

        # 122 不支給情報画面: 不支給理由「その他」選択 不支給開始月「202308」不支給終了月「202308」不支給決定年月日「20230810」
        self.form_input_by_id(idstr="CmbFRiyu", text=riyu)
        self.form_input_by_id(idstr="TxtFStartYM", value=start_ymd)
        self.form_input_by_id(idstr="TxtFEndYM", value=end_ymd)
        self.form_input_by_id(idstr="TxtFKetteiYMD", value=kettei_ymd)
        self.screen_shot("不支給情報画面_122")

        # 123 不支給情報画面:「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.screen_shot("障害児福祉手当資格管理画面_123")

        # 124 特別障害者手当資格管理画面:「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 125 特別障害者手当資格管理画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_125")

        # 126 特別障害者手当資格管理画面:「支払履歴」ボタン押下
        self.click_button_by_label("支払履歴")
        self.screen_shot("支払履歴画面_126")
        
        # 127 支払履歴画面:「戻る」ボタン押下
        self.return_click()
        self.screen_shot("障害児福祉手当資格管理画面_127")
