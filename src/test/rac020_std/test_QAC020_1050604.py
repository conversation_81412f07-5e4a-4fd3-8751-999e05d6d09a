from base.fukushi_case import FukushiSiteTestCaseBase



class TestQAC020_1050604(FukushiSiteTestCaseBase):
    """TestQAC020_1050604"""

    def setUp(self):
        super().setUp()

    # 有期関連書類の提出により、有期更新されなかった対象者に、資格喪失の情報を登録できることを確認する。（事務としては、資格喪失通知の出力もあり。）
    def test_QAC020_1050604(self):
        """有期認定未更新者の登録"""

        case_data = self.test_data["TestQAC020_1050604"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")
        shinsei_ymd = case_data.get("shinsei_ymd", "")
        kaitei = case_data.get("kaitei", "")
        kettei_ymd = case_data.get("kettei_ymd", "")
        kettei_kekka = case_data.get("kettei_kekka", "")

        # 70 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_70")

        # 71 メインメニュー画面: メインメニューから「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()
        self.screen_shot("個人検索表示用画面_71")

        # 72 個人検索画面:「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索表示用画面_72")
        
        # 73 個人検索画面:「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("福祉制度受給状況参照画面_73")

        # 74 福祉制度受給状況参照画面:「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("障害児福祉手当資格管理画面_74")

        # 75 障害児福祉手当資格管理画面:「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 76 障害児福祉手当資格管理画面: 申請種別「資格喪失」選択 申請理由「障害非該当」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
        self.screen_shot("障害児福祉手当資格管理画面_76")

        # 77 障害児福祉手当資格管理画面:「確定」ボタン押下
        self.click_button_by_label("確定")

        # 78 障害児福祉手当資格管理画面: 申請日「20230601」資格喪失日「20230601」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)
        self.form_input_by_id(idstr="TxtKaitei", value=kaitei)
        self.screen_shot("障害児福祉手当資格管理画面_78")

        # 79 障害児福祉手当資格管理画面:「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 80 障害児福祉手当資格管理画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_80")

        # 81 特別障害者手当資格管理画面:「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 82 障害児福祉手当資格管理画面: 判定日「20230602」判定結果「決定」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value=kettei_ymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=kettei_kekka)
        self.screen_shot("障害児福祉手当資格管理画面_82")

        # 83 障害児福祉手当資格管理画面:「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 84 障害児福祉手当資格管理画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_84")
