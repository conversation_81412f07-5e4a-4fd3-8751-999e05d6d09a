from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050206(FukushiSiteTestCaseBase):
    """TestQAC020_1050206"""

    def setUp(self):
        super().setUp()

    # 支払通知書が作成できることを確認する。
    def test_QAC020_1050206(self):
        """支払通知書作成"""

        case_data = self.test_data["TestQAC020_1050206"]

        # 78 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_78")

        # 79 メインメニュー画面: メインメニューから「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 80 バッチ起動画面: <業務>：障害 <事業>：特別障害者手当 <処理区分>：月次処理 <処理分類>：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))
        self.screen_shot("バッチ起動画面_80")

        # 81 バッチ起動画面:「支払通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_001", ""))

        # 82 バッチ起動画面: 振込年月日「20230810」発行年月日「20230809」
        params = [
            {"title": "対象年月", "type": "text", "value": case_data.get("taishou_nengetsu", "")},
            {"title": "振込年月日", "type": "text", "value": case_data.get("furikomi_nengetsubi", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_82")

        # 83 バッチ起動画面:「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.screen_shot("バッチ起動画面_83")

        # 84 バッチ起動画面:「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_84")

        # 85 ジョブ実行履歴画面:「検索」ボタン押下
        self.wait_job_finished(120, 20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_85")

        # 86 ジョブ実行履歴画面:「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("ジョブ帳票履歴画面_86")

        # 87 ジョブ帳票履歴画面:「検索」ボタン押下
        # 88 ジョブ帳票履歴画面:「支払通知書」のNoボタン押下
        # 89 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 90 支払通知書（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime, target_report_name=case_data.get("target_report_name", ""))
        self.screen_shot("ジョブ帳票履歴画面_87")
