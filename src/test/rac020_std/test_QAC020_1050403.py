from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC010_1050403(FukushiSiteTestCaseBase):
    """TestQAC010_1050403"""

    def setUp(self):
        super().setUp()

    # 資格喪失届を提出した住民に対し決定登録ができることを確認する。
    def test_QAC010_1050403(self):
        """資格喪失処理"""

        case_data = self.test_data["TestQAC010_1050403"]
        atena_code = case_data.get("atena_code", "")
        date_ymd = case_data.get("date_ymd", "")
        txt_kettei_ymd = case_data.get("txt_kettei_ymd", "")
        kettei_kekka_cmb = case_data.get("kettei_kekka_cmb", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC020")

        # 23 障害児福祉手当受給者台帳画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 24 障害児福祉手当受給者台帳画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 25 障害児福祉手当受給者台帳画面: 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")
        self.screen_shot("提出書類管理画面_25")

        # 26 提出書類管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 27 提出書類管理画面: その他の提出日「20230702」
        self.entry_teishutsu_shorui(shorui_name="その他", is_check=True, date_ymd=date_ymd)
        self.screen_shot("提出書類管理画面_27")

        # 28 提出書類管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.screen_shot("障害児福祉手当受給者台帳画面_28")

        # 29 障害児福祉手当受給者台帳画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 30 障害児福祉手当受給者台帳画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当受給者台帳画面_30")

        # 31 障害児福祉手当受給者台帳画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 32 障害児福祉手当受給者台帳画面: 判定日「20230702」判定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=txt_kettei_ymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=kettei_kekka_cmb)
        self.screen_shot("障害児福祉手当受給者台帳画面_32")

        # 33 障害児福祉手当受給者台帳画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 34 障害児福祉手当受給者台帳画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当受給者台帳画面_34")
