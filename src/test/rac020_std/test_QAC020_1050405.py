from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC010_1050405(FukushiSiteTestCaseBase):
    """TestQAC010_1050405"""

    def setUp(self):
        super().setUp()

    def test_QAC010_1050405(self):
        """未支払特別児童扶養手当請求書作成"""

        case_data = self.test_data["TestQAC010_1050405"]
        atena_code = case_data.get("atena_code", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        report_name_1 = case_data.get("report_name_1", "")
        report_name_2 = case_data.get("report_name_2", "")

        self.do_login()

        # 55 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()
        self.screen_shot("個人検索画面_55")

        # 56 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索画面_56")

        # 57 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 58 受給状況画面: 「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("障害児福祉手当資格管理画面_57")

        # 59 障害児福祉手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        self.screen_shot("帳票印刷画面_59")

        # 60 帳票印刷画面: 「未支払手当請求書」行の印刷チェックボックス選択「未支払障害児福祉手当請求書」行の発行年月日チェックボックス選択「未支払障害児福祉手当請求書」行の発行年月日「20230702」
        self.print_online_reports(
            case_name="帳票印刷画面",
            report_name_list=[report_name_1, report_name_2],
            hakkou_ymd=hakkou_ymd
        )

        # 61 帳票印刷画面: 「印刷」ボタン押下

        # 62 帳票印刷画面: 未支払手当請求書「ファイルを開く(O)」ボタンを押下

        # 63 未支払手当請求書（PDF）: ×ボタン押下でPDFを閉じる

        # 64 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
