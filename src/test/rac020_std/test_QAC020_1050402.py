from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050402(FukushiSiteTestCaseBase):
    """TestQAC020_1050402"""

    def setUp(self):
        super().setUp()
        
    # 不備があった書類の登録ができることを確認する。
    def test_QAC020_1050402(self):

        case_data = self.test_data["TestQAC020_1050402"]
        atena_code = case_data.get("atena_code", "")
        shorui_name = case_data.get("shorui_name", "")

        # 13 メインメニュー画面:「総合福祉メニュー」
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC020")
        
        # 14 障害児福祉手当受給者台帳画面: 障害児福祉手当受給者台帳画面から「修正」ボタン押下
        self.click_button_by_label("修正")
        
        # 15 障害児福祉手当受給者台帳画面: 「確定」ボタン押下
        self.click_button_by_label("確定")   
        self.screen_shot("障害児福祉手当資格管理画面_15")

        # 16 障害児福祉手当受給者台帳画面:「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")  
        self.screen_shot("提出書類管理画面_16")

        # 17 提出書類管理画面:「追加」ボタン押下
        self.click_button_by_label("追加")
        
        # 18 提出書類管理画面: その他にチェック
        self.entry_teishutsu_shorui(shorui_name=shorui_name, is_check=True)
        self.screen_shot("提出書類管理画面_18")

        # 19 提出書類管理画面:「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.screen_shot("障害児福祉手当資格管理画面_19")

        # 20 障害児福祉手当資格管理画面:「月額計算」ボタン押下
        self.click_button_by_label("月額計算")
        
        # 21 障害児福祉手当資格管理画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_21")
