from selenium.webdriver.common.by import By

from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050202(FukushiSiteTestCaseBase):
    """TestQAC020_1050202"""

    def setUp(self):
        super().setUp()

    # 債権情報の登録できることを確認する。
    def test_QAC020_1050202(self):
        """債権情報登録"""

        case_data = self.test_data["TestQAC020_1050202"]
        nintei_code = case_data.get("nintei_code", "")

        # 11 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_11")

        # 12 メインメニュー画面: メインメニューから「債権管理」ボタン押下
        self.saiken_kanri_click()

        # 13 債権履歴画面: 事業「障害児福祉手当」選択 絞り込み条件「未返納」チェック
        self.form_input_by_id(idstr="CmbGyomu", text=case_data.get("cmb_gyomu", ""))
        self.form_input_by_id(idstr="RadioM", value=case_data.get("radio_m", ""))
        self.screen_shot("債権履歴画面_13")

        # 14 債権履歴画面:「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("債権履歴画面_14")

        # 15 債権履歴画面: 該当者一覧「1」ボタン押下
        nintei_col = '3'
        tb_Page = self.find_element_by_xpath('//*[@id="_wr_body_panel"]/table[5]/tbody/tr/td/b')
        maxPage = str(tb_Page.text).replace("／", "")
        for page_index in range(int(maxPage)):
            tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
            table_idx = 0
            for elem in tr_elem:
                table_idx += 1
                td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + nintei_col + ")")
                if nintei_code == td_elem.text:
                    self.click_by_id("span_Sel" + str(table_idx))
                    break
            self.click_by_id("CmdNextPage")
        self.screen_shot("債権情報画面_15")

        # 16 債権情報画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        # 17 債権情報画面: 調整債権区分「全額債権」チェック
        self.form_input_by_id(idstr="RadioZengakuS", value=case_data.get("radio_zengaku_s", ""))
        self.screen_shot("債権情報画面_17")

        # 18 債権情報画面:「計算」ボタン押下
        self.click_button_by_label("計算")

        # 19 債権情報画面:「債権入力」ボタン押下
        self.click_button_by_label("債権入力")

        # 20 債権情報画面: 債務承認日「20230502」
        self.form_input_by_id(idstr="TxtSaimuYMD", value=case_data.get("txt_saimu_ymd", ""))
        self.screen_shot("債権情報画面_20")

        # 21 債権情報画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.screen_shot("債権履歴画面_21")

        # 22 債権情報画面:「戻る」ボタン押下
        self.return_click()
        self.screen_shot("メインメニュー画面_22")
