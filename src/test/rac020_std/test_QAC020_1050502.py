from base.fukushi_case import FukushiSiteTestCaseBase



class TestQAC020_1050502(FukushiSiteTestCaseBase):
    """TestQAC020_1050502"""

    def setUp(self):
        super().setUp()

    # 資格履歴より所得状況届が出力できることを確認する。
    def test_QAC020_1050502(self):

        case_data = self.test_data["TestQAC020_1050502"]
        atena_code = case_data.get("atena_code", "")

        # 109 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_109")

        # 110 メインメニュー画面: メインメニューから「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()
        
        # 111 個人検索表示用画面:「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索表示用画面_111")

        # 112 個人検索表示用画面:「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("福祉制度受給状況参照画面_112")

        # 113 福祉制度受給状況参照画面:「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("障害児福祉手当資格管理画面_113")

        # 114 障害児福祉手当資格管理画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        # 115 障害児福祉手当資格管理画面:「現況情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("現況情報")
        self.screen_shot("現況情報画面_115")

        # 116 現況情報画面: 現況履歴一覧「１」Noボタン押下
        self.click_button_by_label("1")

        # 117 現況情報画面:「印刷」ボタン押下
        # 118 現況情報画面: 特別障害者手当　所得状況届「ファイルを開く(O)」ボタンを押下
        # 119 特別障害者手当　所得状況届（PDF）: 障害児福祉手当　所得状況届を×ボタン押下でPDFを閉じる
        self.pdf_output_and_download_no_alert(button_id="BtnInsatsu",case_name="特別障害者手当　所得状況届")
        self.screen_shot("現況情報画面_117")
        
        # 120 現況情報画面: 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("障害児福祉手当資格管理画面_120")
        