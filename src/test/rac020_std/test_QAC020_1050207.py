from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050207(FukushiSiteTestCaseBase):
    """TestQAC020_1050207"""

    def setUp(self):
        super().setUp()

    # 支払対象者の一覧等を出力できること、支払データが作成できることを確認する。
    def test_QAC020_1050207(self):
        """支払データ作成"""

        case_data = self.test_data["TestQAC020_1050207"]

        # 91 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_91")

        # 92 メインメニュー画面: メインメニューから「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 93 バッチ起動画面: <業務>：障害 <事業>：特別障害者手当 <処理区分>：月次処理 <処理分類>：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))
        self.screen_shot("バッチ起動画面_93")

        # 94 バッチ起動画面:「支払更新処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_001", ""))

        # 95 バッチ起動画面: 支払区分「定例」選択 対象年月「202308」振込年月日「20230810」依頼日「20230725」
        params = [
            {"title": "支払区分", "type": "select", "value": case_data.get("shiharai_kubun", "")},
            {"title": "対象年月", "type": "text", "value": case_data.get("taishou_nengetsu", "")},
            {"title": "振込年月日", "type": "text", "value": case_data.get("furikomi_nengetsubi", "")},
            {"title": "依頼日", "type": "text", "value": case_data.get("irai_bi", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_95")

        # 96 バッチ起動画面:「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.screen_shot("バッチ起動画面_96")

        # 97 バッチ起動画面:「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_97")

        # 98 ジョブ実行履歴画面:「検索」ボタン押下
        self.wait_job_finished(120, 20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_98")

        # 99 ジョブ実行履歴画面:「ダウンロード」ボタン押下
        # 100 ダウンロード画面: ダウンロードファイル一覧「1」Noボタン押下
        # 101 ジョブ実行履歴画面:「ファイルを開く」ボタン押下
        # 102 QAC010: ×ボタン押下でファイルを閉じる
        exists_down_load_page = self.goto_output_files_dl_page(exec_datetime=exec_datetime)
        if (exists_down_load_page):
            # 作成データぺージに遷移出来た場合は、全ファイルの取得を行う。（戻値はDLしたファイル数）
            output_file_dl_count = self.get_job_output_files(case_name="dl_file")

            self.screen_shot("ダウンロード画面_100")
            # 作成データページに遷移出来てる場合は戻るボタンで実行履歴に戻る。
            self.return_click()

        # 103 ジョブ実行履歴画面:「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("ジョブ帳票履歴画面_103")

        # 104 ジョブ帳票履歴画面:「検索」ボタン押下
        # 105 ジョブ帳票履歴画面:「銀行別集計表」のNoボタン押下
        # 106 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 107 銀行別集計表（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_1", ""))
        self.screen_shot("ジョブ帳票履歴画面_107")

        # 108 ジョブ帳票履歴画面:「口座振替依頼書」のNoボタン押下
        # 109 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 110 口座振替依頼書（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_2", ""))
        self.screen_shot("ジョブ帳票履歴画面_110")

        # 111 ジョブ帳票履歴画面:「支払明細一覧＿銀行支店別」のNoボタン押下
        # 112 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 113 支払明細一覧＿銀行支店別（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_3", ""))
        self.screen_shot("ジョブ帳票履歴画面_113")
