from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050205(FukushiSiteTestCaseBase):
    """TestQAC020_1050205"""

    def setUp(self):
        super().setUp()

    # 定期はまた随時支払対象者を抽出できることを確認する。
    def test_QAC020_1050205(self):
        """支払計算処理"""

        case_data = self.test_data["TestQAC020_1050205"]

        # 53 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_53")

        # 54 メインメニュー画面: メインメニューから「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 55 バッチ起動画面: <業務>：障害 <事業>：特別障害者手当 <処理区分>：月次処理 <処理分類>：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))
        self.screen_shot("バッチ起動画面_55")

        # 56 バッチ起動画面:「支払計算処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_001", ""))

        # 57 バッチ起動画面: 支払区分「定例」選択 対象年月「202308」振込年月日「20230810」発行年月日「20230731」
        params = [
            {"title": "支払区分", "type": "select", "value": case_data.get("shiharai_kubun", "")},
            {"title": "対象年月", "type": "text", "value": case_data.get("taishou_nengetsu", "")},
            {"title": "振込年月日", "type": "text", "value": case_data.get("furikomi_nengetsubi", "")},
            {"title": "発行年月日", "type": "text", "value": case_data.get("hakkou_nengappi", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_57")

        # 58 バッチ起動画面:「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.screen_shot("バッチ起動画面_58")

        # 59 バッチ起動画面:「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_59")

        # 60 ジョブ実行履歴画面:「検索」ボタン押下
        self.wait_job_finished(120, 20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_60")

        # 61 ジョブ実行履歴画面:「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("ジョブ帳票履歴画面_61")

        # 62 ジョブ帳票履歴画面:「検索」ボタン押下
        # 63 ジョブ帳票履歴画面:「支払集計表」のNoボタン押下
        # 64 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 65 支払集計表（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_1", ""))
        self.screen_shot("ジョブ帳票履歴画面_65")

        # 66 ジョブ帳票履歴画面:「支給内訳書」のNoボタン押下
        # 67 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 68 支給内訳書（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_2", ""))
        self.screen_shot("ジョブ帳票履歴画面_68")

        # 69 ジョブ帳票履歴画面:「支払内訳書」のNoボタン押下
        # 70 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 71 支払内訳書（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_3", ""))
        self.screen_shot("ジョブ帳票履歴画面_71")

        # 72 ジョブ帳票履歴画面:「障害区分別内訳表」のNoボタン押下
        # 73 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 74 障害区分別内訳表（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_4", ""))
        self.screen_shot("ジョブ帳票履歴画面_74")

        # 75 ジョブ帳票履歴画面:「振込エラーリスト」のNoボタン押下
        # 76 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 77 振込エラーリスト（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_5", ""))
        self.screen_shot("ジョブ帳票履歴画面_74")
