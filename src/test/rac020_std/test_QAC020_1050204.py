from selenium.webdriver.common.by import By

from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050204(FukushiSiteTestCaseBase):
    """TestQAC020_1050204"""

    def setUp(self):
        case_data = self.test_data["TestQAC020_1050204"]
        super().setUp()

    # 内払い調整の登録ができることを確認する。
    def test_QAC020_1050204(self):
        """内払い調整登録"""

        case_data = self.test_data["TestQAC020_1050204"]
        atena_code = case_data.get("atena_code", "")

        # 43 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_43")

        # 44 メインメニュー画面: メインメニューから「支払調整」ボタン押下
        self.shiharai_chousei_click()
        self.screen_shot("支払調整履歴画面_44_1")

        self.form_input_by_id(idstr="CmbGyomu", text=case_data.get("cmb_gyomu", ""))
        self.form_input_by_id(idstr="RadioMC", value=case_data.get("radio_mc", ""))
        self.click_button_by_label("検索")
        self.screen_shot("支払調整履歴画面_44_2")

        # 45 支払調整履歴画面:	該当者一覧 「1」Noボタン押下
        atena_col = '3'
        sonzaiFlg = False
        tb_Page = self.find_element_by_xpath('//*[@id="_wr_body_panel"]/table[5]/tbody/tr/td/b')
        maxPage = str(tb_Page.text).replace("／", "")
        for page_index in range(int(maxPage)):
            tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
            table_idx = 0
            for elem in tr_elem:
                table_idx += 1
                td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_col + ")")
                if atena_code == td_elem.text:
                    sonzaiFlg = True
                    self.click_by_id("span_Sel" + str(table_idx))
                    break
            if sonzaiFlg == False:
                self.click_by_id("CmdNextPage")
            else:
                break
        self.screen_shot("支払調整登録画面_45")

        # 46 支払調整登録画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        # 47 支払調整登録画面:	調整債権区分「全額調整」チェック 返納予定額「0」
        self.form_input_by_id(idstr="RadioZengakuC", value=case_data.get("radio_zengaku_c", ""))
        self.form_input_by_id(idstr="TxtHYoteiGaku", value=case_data.get("txt_h_yotei_gaku", ""))
        self.screen_shot("支払調整登録画面_47")

        # 48 支払調整登録画面:「計算」ボタン押下
        self.click_button_by_label("計算")

        # 49 支払調整登録画面:	調整額「10000」
        for i in range(1, len(case_data.get("txt_chosei_gaku", {})) + 1):
            key = f"txt_chosei_gaku_{i}"
            value = case_data.get("txt_chosei_gaku", {}).get(key, "")
            self.form_input_by_id(idstr=f"TxtChoseiGaku_{i}", value=value)
        self.screen_shot("支払調整登録画面_49")

        # 50 支払調整登録画面:「差引支払額計算」ボタン押下
        self.click_button_by_label("差引支払額計算")

        # 51 支払調整登録画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("支払調整登録画面_51")

        # 52 支払調整履歴画面:「戻る」ボタン押下
        self.return_click()
        self.screen_shot("支払調整履歴画面_52")
