from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050601(FukushiSiteTestCaseBase):
    """TestQAC020_1050601"""

    def setUp(self):
        super().setUp()

    # 以下のEUCが正しく抽出できることを確認する。・障害児福祉手当_受給者一覧・障害児福祉手当_差止一覧・障害児福祉手当_現況一覧・障害児福祉手当_税無し及び未申告者・障害児福祉手当_履歴一覧
    def test_QAC020_1050601(self):
        """必要に応じて業務データの抽出・確認_資格関連_"""
        # 1	メインメニュー画面	表示
        self.do_login()

        # 3 メインメニュー画面 メインメニューより「申請資格管理」ボタン押下
        # 4 個人検索画面 「住民コード」入力
        # 5 個人検索画面 「検索」ボタン押下
        # 6 受給状況画面 「障害児福祉手当」ボタン押下
        # 7 "障害児福祉手当
        # 資格管理画面" 「修正」ボタン押下
        # 8 "障害児福祉手当
        # 資格管理画面" 有期認定年月１「202305」
        # 9 "障害児福祉手当
        # 資格管理画面" 「月額計算」ボタン押下
        # 10 "障害児福祉手当
        # 資格管理画面" 「登録」ボタン押下
        # 11 メインメニュー画面 メインメニューから「バッチ起動」ボタン押下
        # 12 バッチ起動画面 "業務：障害
        # 事業：特別障害者手当
        # 処理区分：月次処理
        # 処理分類：有期認定対象者一覧処理"
        # 13 バッチ起動画面 「有期認定対象者抽出処理」のNoボタン押下
        # 14 バッチ起動画面 "開始年月「202302」
        # 終了年月「202305」"
        # 15 バッチ起動画面 「処理開始」ボタン押下
        # 16 バッチ起動画面 「実行履歴」ボタン押下
        # 17 ジョブ実行履歴画面 「検索」ボタン押下
        # 18 ジョブ実行履歴画面 「帳票履歴」ボタン押下
        # 19 ジョブ帳票履歴画面 「検索」ボタン押下
        # 20 ジョブ帳票履歴画面 「有期認定対象者一覧」のNoボタン押下
        # 21 ジョブ帳票履歴画面 「ファイルを開く」ボタン押下
        # 22 有期認定対象者一覧（PDF） ×ボタン押下でPDFを閉じる
        # 23 ジョブ帳票履歴画面 「処理一覧」ボタン押下
        # 24 ジョブ帳票履歴画面 「処理一覧」ボタン押下
        # 25 バッチ起動画面 「有期到来者の診断書提出依頼出力処理」のNoボタン押下
        # 26 バッチ起動画面 発行年月日「20230702」
        # 27 バッチ起動画面 「処理開始」ボタン押下
        # 28 バッチ起動画面 「実行履歴」ボタン押下
        # 29 ジョブ実行履歴画面 「検索」ボタン押下
        # 30 ジョブ実行履歴画面 「帳票履歴」ボタン押下
        # 31 ジョブ帳票履歴画面 「検索」ボタン押下
        # 32 ジョブ帳票履歴画面 「診断書の提出について」のNoボタン押下
        # 33 ジョブ帳票履歴画面 「ファイルを開く」ボタン押下
        # 34 診断書の提出について（PDF） ×ボタン押下でPDFを閉じる
        # 35 ジョブ帳票履歴画面 「障害状態再審査（診断）請求書」のNoボタン押下
        # 36 ジョブ帳票履歴画面 「ファイルを開く」ボタン押下
        # 37 障害状態再審査（診断）請求書（PDF） ×ボタン押下でPDFを閉じる

