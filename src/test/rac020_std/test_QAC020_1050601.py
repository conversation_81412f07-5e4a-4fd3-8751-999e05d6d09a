from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050601(FukushiSiteTestCaseBase):
    """TestQAC020_1050601"""

    def setUp(self):
        super().setUp()

    def test_QAC020_1050601(self):
        """必要に応じて業務データの抽出・確認_資格関連_"""

        case_data = self.test_data["TestQAC020_1050601"]
        atena_code = case_data.get("atena_code", "")
        yuuki_nintei_nengetsu_1 = case_data.get("yuuki_nintei_nengetsu_1", "")
        gyomu_select = case_data.get("gyomu_select", "")
        jigyo_select = case_data.get("jigyo_select", "")
        shori_kubun_select = case_data.get("shori_kubun_select", "")
        shori_bunrui_select = case_data.get("shori_bunrui_select", "")
        batch_job_001 = case_data.get("batch_job_001", "")
        start_ymd = case_data.get("start_ymd", "")
        end_ymd = case_data.get("end_ymd", "")
        batch_job_002 = case_data.get("batch_job_002", "")
        hakko_ymd = case_data.get("hakko_ymd", "")

        #1 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_1")

        #3 メインメニュー画面: メインメニューより「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()
        self.screen_shot("個人検索表示用画面_3")

        #4 個人検索画面:「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索表示用画面_4")

        #5 個人検索画面:「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("福祉制度受給状況参照画面_5")

        #6 受給状況画面:「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("障害児福祉手当資格管理画面_6")

        #7 障害児福祉手当資格管理画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        #8 障害児福祉手当資格管理画面: 有期認定年月１「202305」
        self.form_input_by_id(idstr="TxtNinteiYMD1", value=yuuki_nintei_nengetsu_1)
        self.screen_shot("障害児福祉手当資格管理画面_8")

        #9 障害児福祉手当資格管理画面:「月額計算」ボタン押下
        self.click_by_id(idstr="CmdGetsugakuKeisan")
        self.screen_shot("障害児福祉手当資格管理画面_9")

        #10 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label(label="登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_10")

        #11 メインメニュー画面: メインメニューから「バッチ起動」ボタン押下
        self.do_login()
        self.batch_kidou_click()
        self.screen_shot("バッチ起動画面_11")

        #12 バッチ起動画面 "業務：障害 事業：特別障害者手当 処理区分：月次処理 処理分類：有期認定対象者一覧処理"
        self.form_input_by_id(idstr="GyomuSelect", text=gyomu_select)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyo_select)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shori_kubun_select)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shori_bunrui_select)
        self.screen_shot("バッチ起動画面_12")

        #13 バッチ起動画面:「有期認定対象者抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label(batch_job_001)

        #14 バッチ起動画面: "開始年月「202302」 終了年月「202305」"
        params = [
            {"title": "開始年月", "type": "text", "value": start_ymd},
            {"title": "終了年月", "type": "text", "value": end_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_14")

        #15 バッチ起動画面:「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_15")

        #16 バッチ起動画面:「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_16")

        #17 ジョブ実行履歴画面:「検索」ボタン押下
        self.wait_job_finished(200, 20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_17")

        #18 ジョブ実行履歴画面:「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("ジョブ帳票履歴画面_18")

        #19 ジョブ帳票履歴画面:「検索」ボタン押下
        #20 ジョブ帳票履歴画面:「有期認定対象者一覧」のNoボタン押下
        #21 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        #22 有期認定対象者一覧（PDF) ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_1", ""))
        self.screen_shot("ジョブ帳票履歴画面_22")

        #23 ジョブ帳票履歴画面:「処理一覧」ボタン押下
        self.click_job_list()

        #24 ジョブ帳票履歴画面:「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        self.screen_shot("バッチ起動画面_24")

        #25 バッチ起動画面:「有期到来者の診断書提出依頼出力処理」のNoボタン押下
        self.click_batch_job_button_by_label(batch_job_002)

        #26 バッチ起動画面: 発行年月日「20230702」
        params = [
            {"title": "発行年月日", "type": "text", "value": hakko_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_26")

        #27 バッチ起動画面:「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_27")

        #28 バッチ起動画面:「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_28")

        #29 ジョブ実行履歴画面:「検索」ボタン押下
        self.wait_job_finished(200, 20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ帳票履歴画面_29")

        #30 ジョブ実行履歴画面:「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("ジョブ帳票履歴画面_30")

        #31 ジョブ帳票履歴画面:「検索」ボタン押下
        #32 ジョブ帳票履歴画面:「診断書の提出について」のNoボタン押下
        #33 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        #34 診断書の提出について（PDF） ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_2", ""))
        self.screen_shot("ジョブ帳票履歴画面_34")

        #35 ジョブ帳票履歴画面:「障害状態再審査（診断）請求書」のNoボタン押下
        #36 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        #37 障害状態再審査（診断）請求書（PDF） ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime,
                                target_report_name=case_data.get("target_report_name_3", ""))
        self.screen_shot("ジョブ帳票履歴画面_37")

