import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070204(FukushiSiteTestCaseBase):
    """TestQAJ010_01070204"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070204"]
        super().setUp()
    
    # 高額サービス費対象者一覧を出力できることを確認する。高額障害福祉サービス等給付費支給（不支給）決定通知書を出力できることを確認する。高額サービス費支払確定処理を実行し支払データを出力できることを確認する。
    def test_QAJ010_01070204(self):
        """決定通知書・支払データ出力"""
        
        case_data = self.test_data["TestQAJ010_01070204"]
        hurikomibi = case_data.get("hurikomibi", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")
        
        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")
        
        # 4 バッチ起動画面: 業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        
        # 5 バッチ起動画面: 事業「障害者総合支援」選択
        self.form_input_by_id(idstr="JigyoSelect", text="障害者総合支援")
        
        # 6 バッチ起動画面: 処理区分「月次処理」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        
        # 7 バッチ起動画面: 処理分類「高額サービス費支払処理」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="高額サービス費支払処理")
        
        # 8 バッチ起動画面: 高額サービス費支払前処理 　ボタン押下
        self.find_element(By.ID,"Sel1").click()
        self.screen_shot("バッチ起動画面_8")
        
        # 9 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_9")
        
        # 10 バッチ起動画面: 「処理開始」ボタン押下
        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")

        params = [
            {"title":"振込日", "type": "text", "value": hurikomibi},
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        # ジョブ実行(実行した日時を保持しておく)
        exec_datetime = self.exec_batch_job()
        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        
        # 11 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.screen_shot("バッチ起動画面_11")
        
        # 12 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # 処理が終わるまで待機する
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        
        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")
        
        # 15 ジョブ実行履歴画面: No.1 高額サービス費支払前処理  の状態が「正常終了」となったらエビデンス取得
        #self.screen_shot("ジョブ実行履歴画面_16")
        
        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()
        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)
        
        # 17 ジョブ帳票履歴画面: 表示
        #self.screen_shot("ジョブ帳票履歴画面_18")
        
        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        
        # 19 ジョブ帳票履歴画面: 表示
        #self.screen_shot("ジョブ帳票履歴画面_20")
        
        # 20 ジョブ帳票履歴画面: 高額サービス費対象者一覧　のNoボタンを押下
        
        # 21 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        #self.screen_shot("帳票（PDF）_22")
        
        # 22 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_23")
        
        # 23 帳票（PDF）: ×ボタン押下でPDFを閉じる
        #self.screen_shot("帳票（PDF）_24")
        
        # 24 ジョブ帳票履歴画面: 高額障害福祉サービス等給付費支給（不支給）決定通知書　のNoボタンを押下
        
        # 25 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        #self.screen_shot("帳票（PDF）_26")
        
        # 26 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_27")
        
        # 27 帳票（PDF）: ×ボタン押下でPDFを閉じる
        #self.screen_shot("帳票（PDF）_28")
        
        # 28 ジョブ帳票履歴画面: 欠字オーバー字リスト　のNoボタンを押下
        
        # 29 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        #self.screen_shot("帳票（PDF）_30")
        
        # 30 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_31")
        
        # 31 帳票（PDF）: ×ボタン押下でPDFを閉じる
        #self.screen_shot("帳票（PDF）_32")
        
        # 32 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list() 

        # 33 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_34")
        
        # 34 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
       
        # 35 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_35")
        
        # 36 バッチ起動画面: 高額サービス費支払確定処理 　ボタン押下
        self.find_element(By.ID,"Sel2").click()
        self.screen_shot("バッチ起動画面_36")
        
        # 37 バッチ起動画面: 「処理開始」ボタン押下
        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")

        params = [
            {"title":"振込年月日", "type": "text", "value": hurikomibi},
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        # ジョブ実行(実行した日時を保持しておく)
        exec_datetime = self.exec_batch_job()
        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        
        # 38 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.screen_shot("バッチ起動画面_38")
        
        # 39 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # 処理が終わるまで待機する
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 40 ジョブ実行履歴画面: 「検索」ボタン押下
        
        # 41 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_41")
        
        # 42 バッチ起動画面: No.1 高額サービス費支払確定処理 の状態が「正常終了」となり、作成データ欄に「ダウンロード」ボタンが表示されたら押下
        
        # 43 ダウンロード画面: No.「１」ボタン押下
        exists_down_load_page = self.goto_output_files_dl_page(exec_datetime=exec_datetime)
        if(exists_down_load_page):
            # 作成データぺージに遷移出来た場合は、全ファイルの取得を行う。（戻値はDLしたファイル数）
            output_file_dl_count = self.get_job_output_files(case_name="dl_file")
            # 作成データページに遷移出来てる場合は戻るボタンで実行履歴に戻る。
            self.return_click()
        
        # 44 テキストエディタ: 「ファイルを開く(O)」ボタンを押下
        
        # 45 テキストエディタ: 表示
        #self.screen_shot("テキストエディタ_46")
        
        # 46 テキストエディタ: ×ボタン押下でを閉じる
        
        # 47 ダウンロード画面: 「戻る」ボタン押下
        
        # 48 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_48")
        
        # 49 ジョブ実行履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 50 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_50")
        
