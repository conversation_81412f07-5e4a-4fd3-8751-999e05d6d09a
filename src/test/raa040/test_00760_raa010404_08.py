import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040408(FukushiSiteTestCaseBase):
    """TESTRAA01040408"""
    
    # 各テストメソッドの実行前に実行したいもの
        
    def test_case_raa010404_08(self):
        """test_case_raa010404_08"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        # 帳票名
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        # 発行年月日
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        
        hantei_data = self.common_test_data.get("TESTRAA01040403", {})
        # 手帳交付方法
        techou_kouhu_houhou = hantei_data.get("techou_kouhu_houhou", "")
        
        if techou_kouhu_houhou == "窓口":
            return
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA040")

        self.click_button_by_label("印刷")
        self.screen_shot("raa010404_08_02")
        
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name,
                "params": [
                    {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        self.screen_shot("raa010404_08_06")
        
        # 「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("raa010404_08_09")
