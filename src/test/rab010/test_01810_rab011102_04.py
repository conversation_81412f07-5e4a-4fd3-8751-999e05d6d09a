import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01110204(FukushiSiteTestCaseBase):
    """TESTRAB01110204"""

    # 個別に支払解除を行えることを確認する。
    def test_case_rab011102_04(self):
        """支払解除"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("[011102-04]_メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("[011102-04]_個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("[011102-04]_受給状況画面_6")

        # 7 受給状況画面: 「補装具費支給」ボタン押下
        self.click_button_by_label("補装具費支給")

        # 8 履歴選択画面: 表示
        self.screen_shot("[011102-04]_履歴選択画面_8")

        # 9 履歴選択画面: 「資格履歴」ボタン押下
        self.click_by_id("CmdButton1_1") 

        # 10 補装具費支給資格管理画面: 表示
        self.screen_shot("[011102-04]_補装具費支給資格管理画面_10")

        # 11 補装具費支給資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 12 補装具費支給資格管理画面: 表示
        self.screen_shot("補装具費支給資格管理画面_12")

        # 13 補装具費支給資格管理画面: 支払日 削除
        self.find_element_by_id("Kofuken_ShiharaiYMD1").clear()

        # 14 補装具費支給資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 15 補装具費支給資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011102-04]_補装具費支給資格管理画面_15")
