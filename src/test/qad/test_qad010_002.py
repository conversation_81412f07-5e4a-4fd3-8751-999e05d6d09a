import time

from base.fukushi_case import FukushiSiteTestCaseBase
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAD010(FukushiSiteTestCaseBase):
    """TESTQAD010002"""

    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAD010-002.sql", params=atena_list)
        super().setUp()
    
    def test_case_qad010_002(self):
        """test_case_qad010_002"""

        #ログイン
        driver = None
        test_data = self.common_test_data
        self.do_login()
        
        #メインメニュー・資格管理ボタン押下
        self.find_element(By.ID,"CmdProcess1_1").click()

        #資格管理画面・宛名コード入力後、検索ボタン押下
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("case_common_qad010_atena_code2",""))
        self.find_element(By.ID,"span_<PERSON>saku").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-5" , True)

        #個人情報画面・障害者医療ボタン押下
        self.find_element(By.ID,"02:0000000030:QAD010").send_keys(Keys.ENTER)
        self.save_screenshot_migrate(driver, "QAD010-002-2-7" , True)

        #資格管理画面・申請内容入力ボタン押下
        self.find_element(By.ID,"CmdShinsei").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-8" , True)

        #資格管理画面・口座情報ボタン押下
        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon14").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-10" , True)

        #口座情報画面・追加ボタン押下
        self.find_element(By.ID,u"口座情報_u_Tuika").click()

        #口座情報画面・金融機関検索ボタン押下
        self.find_element(By.ID,u"口座情報_u_KinyuKikanKensaku").click()

        #金融機関検索画面・検索
        self.find_element(By.ID,"TxtGinkoCode").click()
        self.find_element(By.ID,"TxtGinkoCode").send_keys("")
        self.find_element(By.ID,"TxtGinkoCode").send_keys("1")
        self.find_element(By.ID,"span_CmdKensaku").click()
        self.find_element(By.ID,"CmdShori1").click()

        #口座情報画面・登録
        self.find_element(By.ID,u"口座情報_u_YukoKikanKaishi").click()
        self.find_element(By.ID,u"口座情報_u_YukoKikanKaishi").send_keys("")
        self.find_element(By.ID,u"口座情報_u_YukoKikanKaishi").send_keys("20190101")
        self.find_element(By.ID,u"口座情報_u_KouzaBango").click()
        self.find_element(By.ID,u"口座情報_u_KouzaBango").send_keys("")
        self.find_element(By.ID,u"口座情報_u_KouzaBango").send_keys("1231231")
        self.accept_next_alert = True
        self.find_element(By.ID,u"口座情報_u_Touroku").click()
        self.assertEqual(u"登録します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-12" , True)

        #資格管理画面・保険情報ボタン押下
        self.find_element(By.ID,"btnCommon2").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-14" , True)

        #保険情報一覧画面・追加ボタン押下
        self.find_element(By.ID,"CmdTsuika").click()

        #保険情報登録画面・保険者検索ボタン動確
        self.find_element(By.ID,"span_CmdHokenSerch").click()

        #保険者検索画面・保険者検索
        self.find_element(By.ID,"txtKanaMeisho").click()
        self.find_element(By.ID,"txtKanaMeisho").send_keys("")
        self.find_element(By.ID,"txtKanaMeisho").send_keys(u"フクシ")
        self.find_element(By.ID,"BtnKensaku").click()
        time.sleep(3)
        self.find_element(By.ID,"Sel1").click()

        #保険情報登録画面・被保険者検索ボタン動確
        self.find_element(By.ID,"span_CmdHiHokenSerch").click()
        time.sleep(3)

        #世帯員検索画面・世帯員選択
        self.find_element(By.ID,"Sel1").click()

        #保険情報登録画面・保険情報入力
        self.find_element(By.ID,"FuyoshaCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"FuyoshaCmb"),"本人")
        self.find_element(By.ID,"FuyoshaCmb").click()
        self.find_element(By.ID,"TxtKigo").click()
        self.find_element(By.ID,"TxtKigo").send_keys("")
        self.find_element(By.ID,"TxtKigo").send_keys(u"１２３")
        self.find_element(By.ID,"TxtBango").click()
        self.find_element(By.ID,"TxtBango").send_keys("")
        self.find_element(By.ID,"TxtBango").send_keys(u"１２３４")
        self.find_element(By.ID,"TxtShutokuYMD").click()
        self.find_element(By.ID,"TxtShutokuYMD").send_keys("")
        self.find_element(By.ID,"TxtShutokuYMD").send_keys("20190101")

        #保険情報登録画面・登録ボタン押下
        self.accept_next_alert = True
        self.find_element(By.ID,"CmdHokenToroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        time.sleep(3)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-16" , True)

        #資格管理画面・書類提出管理ボタン押下
        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon12").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-17" , True)

        #提出書類管理画面・追加ボタン押下
        self.find_element(By.ID,"BtnTsuika").click()

        #提出書類管理画面・書類情報登録
        self.find_element(By.ID,"BtnTsuika").click()
        self.find_element(By.ID,"ChkTeisyutsu_1").click()
        self.find_element(By.ID,"TxtSyoruiYMD_1").click()
        self.find_element(By.ID,"TxtSyoruiYMD_1").send_keys("")
        self.find_element(By.ID,"TxtSyoruiYMD_1").send_keys("20210701")
        self.find_element(By.ID,"BtnKanryo").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-20" , True)

        #資格管理画面・確定ボタン押下
        self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"認定請求")
        self.find_element(By.ID,"CmdKakutei").click()
        self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiRiyuuCmb"),"障害認定")
        self.find_element(By.ID,"CmdKakutei").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-23" , True)

        #資格管理画面・申請内容入力/登録
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("")
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("20210701")
        self.find_element(By.ID,"TxtJiyuHasseiYMD").click()
        self.find_element(By.ID,"TxtJiyuHasseiYMD").send_keys("")
        self.find_element(By.ID,"TxtJiyuHasseiYMD").send_keys("20210701")
        self.find_element(By.ID,"TantoShokatsukuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        self.accept_next_alert = True
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-002-2-25" , True)

        #資格管理画面・申請取り下げボタン押下
        self.find_element(By.ID,"CmdTorisage").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-26" , True)

        #資格管理画面・決定内容入力
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("")
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("20210701")
        self.find_element(By.ID,"KetteiKekkaCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"KetteiKekkaCmb"),"取消")

        #資格管理画面・登録ボタン押下
        self.accept_next_alert = True
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-002-2-28" , True)

        #資格管理画面・修正ボタン押下
        self.find_element(By.ID,"CmdShuusei").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-29" , True)

        #資格管理画面・口座削除
        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon14").click()
        self.accept_next_alert = True
        self.find_element(By.ID,u"口座情報_u_Sakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-002-2-31" , True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-33" , True)

        #資格管理画面・提出書類管理修正
        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon12").click()
        self.find_element(By.ID,"Sel1").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-35" , True)
        self.find_element(By.ID,"BtnShusei").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-36" , True)
        self.find_element(By.ID,"BtnKanryo").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-39" , True)

        #資格管理画面・提出書類管理削除
        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon12").click()
        self.find_element(By.ID,"BtnSakujyo").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-42" , True)

        #資格管理画面・登録ボタン押下
        self.accept_next_alert = True
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-002-2-43" , True)

        #資格管理画面・削除ボタン押下
        self.accept_next_alert = True
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-002-2-44" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-45", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-002-2-46", True)