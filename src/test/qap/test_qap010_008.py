# -*- coding: utf-8 -*-
#QAP-001 2022年度以降流す方へ
#年度変更で年月等置換する場合は、年月が大きい値から先に繰り上げて置換してください。
from base.kodomo_case import KodomoSiteTestCaseBase
import unittest

class TestQAP010008(KodomoSiteTestCaseBase):
    def setUp(self):
        # atena_list = self.test_data.get("sql_params")
        super().setUp()
    
    def test_case_qap010_008(self):
        '''test_case_qap010_008'''
        driver = None

        #ログイン
        self.goto_kodomo_riyoutyusei_riyoujyoukyou_search()
        #施設利用状況検索
        self.save_screenshot_migrate(driver, "QAP000-004-004" , True)
        self.find_element_by_id("tab01_QAPF101400_txtShiborikomiShisetsuName_textboxInput").click()
        self.find_element_by_id("tab01_QAPF101400_txtShiborikomiShisetsuName_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF101400_txtShiborikomiShisetsuName_textboxInput").send_keys(u"あ")
        self.find_element_by_id("tab01_QAPF101400_btnJokenKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP000-004-007" , True)
        #待機者管理
        self._goto_taikisya_search()
        self.save_screenshot_migrate(driver, "QAP000-004-008" , True)
        self.find_element_by_id("tab02_QAPF105000_chkMoshikomiKbnchk0").click()
        self.find_element_by_id("tab02_QAPF105000_btnKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP000-004-011" , True)

if __name__ == "__main__":
    unittest.main()
