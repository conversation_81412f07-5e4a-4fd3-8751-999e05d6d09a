import time
from selenium.webdriver.support.select import Select
from base.kodomo_case import KodomoSiteTestCaseBase
import unittest

class TestQAP020129(KodomoSiteTestCaseBase):
    """TestQAP020129"""

    def __init__(self, *args, **kwargs):
        self.env_name = "student"
        super().__init__(*args, **kwargs)
    
    def setUp(self):
        atena_list = self.test_data.get("sql_params")
        self.exec_sqlfile("QAP129_実施前スクリプト.sql", params=atena_list)
        super().setUp()
    
    def test_case_qap020_129(self):
        '''test_case_qap020_129'''
        driver = None
    
        # ログイン
        self.do_login()
        self.find_element_by_id(u"mainmenu_1").click()
        time.sleep(2)
        self.goto_menu(["学童","利用調整","入室自動選考結果（児童検索）"],"1")
        self.save_screenshot_migrate(driver, "QAP129-004" , True)
        time.sleep(2)
        self.find_element_by_id("tab01_ZZZ000000_selSenkoDataSentaku_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_selSenkoDataSentaku_select")).select_by_value(self.test_data.get('選考データ'))
      
        self.click_button_by_label("検索")
        self.save_screenshot_migrate(driver, "QAP129-007" , True)
        self.click_button_by_label("1")
        self.save_screenshot_migrate(driver, "QAP129-009" , True)
        self.click_button_by_label("修正")
        self.find_element_by_id("tab01_ZZZ000000_sel1046HenkouRiyuu_1_15_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_sel1046HenkouRiyuu_1_15_select")).select_by_visible_text(self.test_data.get("変更理由"))
        self.click_button_by_label("登録")  
        self.save_screenshot_migrate(driver, "QAP129-012" , False)
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.save_screenshot_migrate(driver, "QAP129-013" , True)

        #入所自動選考結果（同点施設確認）
        self.goto_menu(["学童","利用調整","入所自動選考結果（同点施設確認）"],"1")
        self.save_screenshot_migrate(driver, "QAP129-015" , True)
        self.find_element_by_id("tab02_ZZZ000000_selSenkoDataSentaku_select").click()
        Select(self.find_element_by_id("tab02_ZZZ000000_selSenkoDataSentaku_select")).select_by_value(self.test_data.get('選考データ'))
        self.find_element_by_id("tab02_QAPF104700_btnKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP129-018" , True)
        self.find_element_by_id("tab02_ZZZ000000_btnShisetsuNo_1_1_button").click()
        self.save_screenshot_migrate(driver, "QAP129-020" , True)
        # self.find_element_by_xpath("//button[@id='btn_back']/span[1]").click()
        self.wait_page_loaded()
        # time.sleep(5)
        # 画面タブを閉じる
        try:       
            els = self.find_elements_by_css_selector("span.removeTab")
            for i in reversed(range(len(els))):
                els[i].click()

        finally:
            pass
        time.sleep(1)
        #入室自動選考結果（施設人数確認変更）
        self.goto_menu(["学童","利用調整","入室自動選考結果（施設人数確認変更）"],"1")
        self.save_screenshot_migrate(driver, "QAP129-021" , True)
        self.find_element_by_id("tab01_ZZZ000000_selSenkoDataSentaku_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_selSenkoDataSentaku_select")).select_by_value(self.test_data.get('選考データ'))
        self.find_element_by_id("tab01_QAPF104300_btnShisetsuKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP129-025" , True)
        self.find_element_by_id("tab01_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").send_keys(self.test_data.get('施設コード'))
        self.find_element_by_id("tab01_QAPF201600_WrCmnBtn05_button").click()
        self.save_screenshot_migrate(driver, "QAP129-028" , True)
        self.find_element_by_id("tab01_QAPF104300_btnKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP129-030" , True)
        self.find_element_by_id("tab01_ZZZ000000_btnRenBan_1_1_button").click()
        self.save_screenshot_migrate(driver, "QAP129-032" , True)
        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP129-034" , True)
        self.find_element_by_id("tab01_ZZZ000000_txtChosei_1_10_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtChosei_1_10_textboxInput").clear()
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_ZZZ000000_txtChosei_1_10_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtChosei_1_10_textboxInput").send_keys("1")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF104300_regbtn_button").click()
        self.save_screenshot_migrate(driver, "QAP129-037" , False)
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.find_element_by_id("tab01_QAPF104300_btnKensaku").click() 
        self.save_screenshot_migrate(driver, "QAP129-038" , True)
        self.find_element_by_id("tab01_QAPF104300_clearbtn_button").click()
        self.find_element_by_css_selector(".popupButton").click()

    def tearDown(self):
        try:       
            els = self.find_elements_by_css_selector("span.removeTab")
            for i in reversed(range(len(els))):
                els[i].click()

        finally:
            return super().tearDown(removeTab=False)   
        
if __name__ == "__main__":
    unittest.main()
