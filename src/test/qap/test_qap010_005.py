# -*- coding: utf-8 -*-
#QAP-005 2024年度以降流す方へ
#年度変更で年月等置換する場合は、年月が大きい値から先に繰り上げて置換してください。
#変更後の入所年月日は世帯員該当期間内でなければならない。
from selenium.webdriver.support.select import Select
from base.kodomo_case import KodomoSiteTestCaseBase
import unittest

class TestQAP010005(KodomoSiteTestCaseBase):
    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params")
        self.exec_sqlfile("QAP005_実施前スクリプト.sql", params=atena_list)
        super().setUp()
    
    
    def test_case_qap010_005(self):
        '''test_case_qap010_005'''
        driver = None
        test_data = self.test_data 
        # ログイン
        self.goto_kodomo_setaijyouhou_search()
        self.save_screenshot_migrate(driver, "QAP005-001-004" , True)
        self.find_element_by_id("tab01_QAZF100001_txtJuminCD_textboxInput").click()
        self.find_element_by_id("tab01_QAZF100001_txtJuminCD_textboxInput").clear()
        self.find_element_by_id("tab01_QAZF100001_txtJuminCD_textboxInput").send_keys(test_data.get("case_qap001_atena_code3"))
        self.find_element_by_id("tab01_QAZF100001_cond_item_0_9_").click()
        self.find_element_by_id("tab01_QAZF100001_WrCmnBtn05_button").click()
        self.save_screenshot_migrate(driver, "QAP005-001-007" , True)
        self.find_element_by_id("tab01_ZZZ000000_btnSetaiRirekiNo_1_1_button").click()
        self.find_element_by_id("tab01_ZZZ000000_btnJIDoIchiranRemban_2_1_button").click()
        self.save_screenshot_migrate(driver, "QAP005-001-009" , True)
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        self.find_element_by_id("tab01_QAPF103500_btnZuijiShinsei_button").click()
        self.save_screenshot_migrate(driver, "QAP005-001-011" , True)
        self.find_element_by_id("tab01_QAPF109000_selKisoInfoJotaiKbn_select").click()
        Select(self.find_element_by_id("tab01_QAPF109000_selKisoInfoJotaiKbn_select")).select_by_visible_text(u"申込")
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoUketsukeYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoUketsukeYMD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoUketsukeYMD_textboxInput").send_keys("20230401")
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoShinseiYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoShinseiYMD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoShinseiYMD_textboxInput").send_keys("20230401")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoShinseiYukoKikan_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoShinseiYukoKikan_textboxInput").send_keys("20230401")
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoKiboKikanStart_textboxInput").click()
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoKiboKikanStart_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF109000_txtKisoInfoKiboKikanStart_textboxInput").send_keys("20230401")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF109000_btnKisoInfoKiboKikanShu_button").click()
        self.find_element_by_id("tab01_QAPF109000_selWrGyoseiku_KisoInfoSyokanKu_select").click()
        Select(self.find_element_by_id("tab01_QAPF109000_selWrGyoseiku_KisoInfoSyokanKu_select")).select_by_visible_text(u"テスト一区")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF109000_selWrGyoseiku_KisoInfoUketsukeKu_select").click()
        Select(self.find_element_by_id("tab01_QAPF109000_selWrGyoseiku_KisoInfoUketsukeKu_select")).select_by_visible_text(u"テスト一区")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF109000_btnShisetsuSentaku_button").click()
        self.save_screenshot_migrate(driver, "QAP005-001-020" , True)
        self.find_element_by_id("tab01_QAPF103900_txtShisetsuCD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103900_txtShisetsuCD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103900_txtShisetsuCD_textboxInput").send_keys("9999")
        self.find_element_by_id("tab01_QAPF103900_btnKensaku_button").click()
        self.find_element_by_id("tab01_ZZZ000000_btnChushutsuNoButton_1_1_button").click()
        self.find_element_by_id("tab01_QAPF103900_btnSentakuKakuteiButtons_button").click()
        self.save_screenshot_migrate(driver, "QAP005-001-025" , True)
        self.find_element_by_id("tab01_QAPF109000_btnShisu_button").click()
        self.save_screenshot_migrate(driver, "QAP005-002-027" , True)
        self.find_element_by_id("tab01_QAPF109200_btnRegCloses_button").click()
        self.save_screenshot_migrate(driver, "QAP005-002-032" , True)
        self.find_element_by_id("tab01_QAPF109000_btnKiboshisetsu_button").click()
        self.find_element_by_id("tab01_QAPF109100_btnRegCloses_button").click()
        self.find_element_by_id("tab01_QAPF109000_regbtn_button").click()
        self.save_screenshot_migrate(driver, "QAP005-003-036" , True)
        self.find_element_by_id("tempId__1").click()
        self.save_screenshot_migrate(driver, "QAP005-003-037" , True)
        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP005-003-039" , True)
        self.find_element_by_id("tab01_QAPF103500_btnNyushoKanri_button").click()
        self.save_screenshot_migrate(driver, "QAP005-003-041" , True)
        self.find_element_by_id("tab01_QAPF103700_btnShuseiTorisage_button").click()
        self.save_screenshot_migrate(driver, "QAP005-003-043" , True)
        self.find_element_by_id("tab01_QAPF103700_txtTorisageYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103700_txtTorisageYMD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103700_txtTorisageYMD_textboxInput").send_keys("20230401")
        self.find_element_by_xpath(u"(.//*[normalize-space(text()) and normalize-space(.)='取下げ'])[3]/following::table[1]").click()
        self.find_element_by_id("tab01_QAPF103700_selTorisageKoshinKbn_select").click()
        Select(self.find_element_by_id("tab01_QAPF103700_selTorisageKoshinKbn_select")).select_by_visible_text(u"取下げ登録・変更")
        self.find_element_by_xpath(u"(.//*[normalize-space(text()) and normalize-space(.)='取下げ'])[3]/following::table[1]").click()
        self.find_element_by_id("tab01_QAPF103700_selTorisageRiyu_select").click()
        Select(self.find_element_by_id("tab01_QAPF103700_selTorisageRiyu_select")).select_by_visible_text(u"その他")
        self.find_element_by_id("tab01_QAPF103700_regbtn_button").click()
        self.save_screenshot_migrate(driver, "QAP005-003-048" , True)
        self.find_element_by_id("tempId__3").click()
        self.save_screenshot_migrate(driver, "QAP005-003-049" , True)
        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP005-003-051" , True)
        self.find_element_by_id("tab01_QAPF103500_btnNyushoMoushikomi_button").click()
        self.save_screenshot_migrate(driver, "QAP005-003-053" , True)
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoUketsukeYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoUketsukeYMD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoUketsukeYMD_textboxInput").send_keys("20230401")
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoShinseiYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoShinseiYMD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoShinseiYMD_textboxInput").send_keys("20230401")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoShinseiYukoKikan_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoShinseiYukoKikan_textboxInput").send_keys("20230401")
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoKiboKikanStart_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoKiboKikanStart_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103700_txtKisoInfoKiboKikanStart_textboxInput").send_keys("20230401")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF103700_btnKisoInfoKiboKikanShu_button").click()
        self.find_element_by_id("tab01_QAPF103700_selWrGyoseiku_KisoInfoSyokanKu_select").click()
        Select(self.find_element_by_id("tab01_QAPF103700_selWrGyoseiku_KisoInfoSyokanKu_select")).select_by_visible_text(u"テスト一区")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF103700_selWrGyoseiku_KisoInfoUketsukeKu_select").click()
        Select(self.find_element_by_id("tab01_QAPF103700_selWrGyoseiku_KisoInfoUketsukeKu_select")).select_by_visible_text(u"テスト一区")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_xpath("//li[@id='tab01_QAPF103700_kiboshisetsu_li']/a/span").click()
        self.find_element_by_id("tab01_QAPF103700_btnKiboShisetsuKyodaiSetteiNaiyoCopy_button").click()
        self.save_screenshot_migrate(driver, "QAP005-003-063" , True)
        self.find_element_by_id("tab01_ZZZ000000_btnJidoNo_1_1_button").click()
        self.save_screenshot_migrate(driver, "QAP005-004-065" , True)
        self.find_element_by_id("tab01_QAPF103700_regbtn_button").click()
        self.save_screenshot_migrate(driver, "QAP005-004-067" , True)
        self.find_element_by_id("tempId__5").click()
        self.save_screenshot_migrate(driver, "QAP005-004-068" , True)
        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP005-004-070" , True)
        self.find_element_by_id("tab01_QAPF103500_btnSenkoKekkaKoshin_button").click()
        self.save_screenshot_migrate(driver, "QAP005-004-072" , True)
        self.find_element_by_id("tab01_QAPF105500_btnAddChg_button").click()
        self.save_screenshot_migrate(driver, "QAP005-004-074" , True)
        self.find_element_by_id("tab01_ZZZ000000_selSenkoKekkaInputKetteiShisetsu_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_selSenkoKekkaInputKetteiShisetsu_select")).select_by_visible_text(u"（幼）テスト施設修正")
        self.find_element_by_xpath(u"(.//*[normalize-space(text()) and normalize-space(.)='希望順位'])[1]/following::td[1]").click()
        self.find_element_by_xpath("//button[@id='tab01_QAPF105500_regbtn_button']/img").click()
        self.save_screenshot_migrate(driver, "QAP005-004-084" , True)
        self.find_element_by_id("tempId__7").click()
        self.save_screenshot_migrate(driver, "QAP005-004-085" , True)
        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP005-004-087" , True)
        self.find_element_by_id("tab01_QAPF103500_btnNyushoKanri_button").click()
        self.save_screenshot_migrate(driver, "QAP005-004-089" , True)
        self.find_element_by_id("tab01_QAPF103700_btnNyusyoKikanUpd_button").click()
        self.save_screenshot_migrate(driver, "QAP005-004-091" , True)
        self.find_element_by_id("tab01_QAPF106700_txtNyuusyoYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF106700_txtNyuusyoYMD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF106700_txtNyuusyoYMD_textboxInput").send_keys(self.test_data.get("txtNyuusyoYMD_textboxInput"))
        self.find_element_by_id("tab01_QAPF106700_txtNyuusyoBanngo_textboxInput").click()
        self.find_element_by_id("tab01_QAPF106700_btnTouroku_button").click()
        self.find_element_by_id("tempId__9").click()
        self.save_screenshot_migrate(driver, "QAP005-004-094" , True)
        self.find_element_by_id("tab01_QAPF106700_btnClose").click()
        self.save_screenshot_migrate(driver, "QAP005-004-096" , True)
        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP005-005-098" , True)
        self.find_element_by_id("tab01_QAPF103500_btnKeiyakuINFO_button").click()
        self.save_screenshot_migrate(driver, "QAP005-005-100" , True)
        self.find_element_by_id("tab01_QAPF103800_btnAddChg_button").click()
        self.save_screenshot_migrate(driver, "QAP005-005-102" , True)
        self.find_element_by_id("tab01_QAPF103800_txtTodokedeYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103800_txtTodokedeYMD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103800_txtTodokedeYMD_textboxInput").send_keys("20230401")
        self.find_element_by_id("tab01_QAPF103800_txtKeiyakuYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103800_txtKeiyakuYMD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103800_txtKeiyakuYMD_textboxInput").send_keys("20230401")
        self.find_element_by_id("tab01_QAPF103800_txtRiyoKikanStart_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103800_txtRiyoKikanStart_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF103800_txtRiyoKikanStart_textboxInput").send_keys("20231001")
        self.find_element_by_id("tab01_QAPF103800_selKeiyakuNinteiKbn_select").click()
        Select(self.find_element_by_id("tab01_QAPF103800_selKeiyakuNinteiKbn_select")).select_by_visible_text(u"１号")
        self.find_element_by_id("tab01_QAPF103800_selKeiyakuHoikuHitsuyoryo_select").click()
        Select(self.find_element_by_id("tab01_QAPF103800_selKeiyakuHoikuHitsuyoryo_select")).select_by_visible_text(u"教育標準時間")
        self.find_element_by_id("tab01_QAPF103800_chkEnchoHoikuRiyoUmuchk0").click()
        self.find_element_by_id("tab01_QAPF103800_regbtn_button").click()
        self.save_screenshot_migrate(driver, "QAP005-005-109" , True)
        self.find_element_by_id("tempId__11").click()
        self.save_screenshot_migrate(driver, "QAP005-005-110" , True)


if __name__ == "__main__":
    unittest.main()
