# -*- coding: utf-8 -*-
import time
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010028(KodomoSiteTestCaseBase):
    def setUp(self):
        atena_list = self.test_data.get("sql_params")
        self.exec_sqlfile("QAP029_実施前スクリプト.sql", params=atena_list)
        super().setUp()
    
    def test_case_qap010_029(self):
        '''test_case_qap010_029'''
        driver = None
        
        #ログイン
        #入所自動選考結果（児童検索)
        self.goto_kodomo_riyoutyousei_jidousennkoujidoukennsaku()        
        
        self.save_screenshot_migrate(driver, "QAP029-004" , True)
        self.find_element_by_id("tab01_ZZZ000000_selSenkoDataSentaku_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_selSenkoDataSentaku_select")).select_by_visible_text(u"[10012] 2017/01/23 1100 OSテスト 本処理 2017/01/23 20:16:51 選考1回目 1100")
        self.find_element_by_id("tab01_QAPF104400_btnKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP029-007" , True)
        self.find_element_by_id("tab01_ZZZ000000_btnHogoshaNo_1_1_button").click()
        self.save_screenshot_migrate(driver, "QAP029-009" , True)
        self.find_element_by_xpath("//button[@id='tab01_QAPF104600_btnEditChg_button']/img").click()
        self.find_element_by_id("tab01_ZZZ000000_sel1046HenkouRiyuu_1_15_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_sel1046HenkouRiyuu_1_15_select")).select_by_visible_text(u"兄弟同時入所のため")
        self.find_element_by_xpath("//button[@id='tab01_QAPF104600_regbtn_button']/img").click()
        self.save_screenshot_migrate(driver, "QAP029-012" , False)
        self.find_element_by_id("tempId__1").click()
        self.save_screenshot_migrate(driver, "QAP029-013" , True)

        #入所自動選考結果（同点施設確認）
        self._goto_jidousennkoukekka_doutennsisetukakuninn()
        self.save_screenshot_migrate(driver, "QAP029-015" , True)
        self.find_element_by_id("tab02_ZZZ000000_selSenkoDataSentaku_select").click()
        Select(self.find_element_by_id("tab02_ZZZ000000_selSenkoDataSentaku_select")).select_by_visible_text(u"[10012] 2017/01/23 1100 OSテスト 本処理 2017/01/23 20:16:51 選考1回目 1100")
        self.find_element_by_id("tab02_QAPF104700_btnKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP029-018" , True)
        self.find_element_by_id("tab02_ZZZ000000_btnShisetsuNo_1_1_button").click()
        self.save_screenshot_migrate(driver, "QAP029-020" , True)
        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        time.sleep(5)
        #入所自動選考結果（施設人数確認変更）
        self._goto_jidousennkoukekka_sisetuninnzuukakuninnhennkou()
        self.save_screenshot_migrate(driver, "QAP029-021" , True)
        self.find_element_by_id("tab03_ZZZ000000_selSenkoDataSentaku_select").click()
        Select(self.find_element_by_id("tab03_ZZZ000000_selSenkoDataSentaku_select")).select_by_visible_text(u"[10012] 2017/01/23 1100 OSテスト 本処理 2017/01/23 20:16:51 選考1回目 1100")
        self.find_element_by_id("tab03_QAPF104300_btnShisetsuKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP029-025" , True)
        self.find_element_by_id("tab03_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").click()
        self.find_element_by_id("tab03_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").clear()
        self.find_element_by_id("tab03_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").send_keys(self.test_data.get('QAP010_SHISETSU'))
        self.find_element_by_id("tab03_QAPF201600_WrCmnBtn05_button").click()
        self.save_screenshot_migrate(driver, "QAP029-028" , True)
        self.find_element_by_id("tab03_QAPF104300_btnKensaku_button").click()
        self.save_screenshot_migrate(driver, "QAP029-030" , True)
        self.find_element_by_id("tab03_ZZZ000000_btnRenBan_1_1_button").click()
        self.save_screenshot_migrate(driver, "QAP029-032" , True)
        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP029-034" , True)
        self.find_element_by_id("tab03_ZZZ000000_txtChosei_1_10_textboxInput").click()
        self.find_element_by_id("tab03_ZZZ000000_txtChosei_1_10_textboxInput").clear()
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab03_ZZZ000000_txtChosei_1_10_textboxInput").click()
        self.find_element_by_id("tab03_ZZZ000000_txtChosei_1_10_textboxInput").send_keys("1")
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab03_QAPF104300_regbtn_button").click()
        self.save_screenshot_migrate(driver, "QAP029-037" , False)
        self.find_element_by_id("tempId__3").click()
        self.find_element_by_id("tab03_QAPF104300_btnKensaku").click() 
        self.save_screenshot_migrate(driver, "QAP029-038" , True)
        self.find_element_by_id("tab03_QAPF104300_clearbtn_button").click()
        self.find_element_by_css_selector(".popupButton").click()
    
    def tearDown(self):
        try:       
            els = self.find_elements_by_css_selector("span.removeTab")
            for i in reversed(range(len(els))):
                els[i].click()

        finally:
            return super().tearDown(removeTab=False)        

if __name__ == "__main__":
    unittest.main()
